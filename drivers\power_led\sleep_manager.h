#ifndef __SLEEP_MANAGER_H
#define __SLEEP_MANAGER_H

#include "CH58x_common.h"
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

// 复位标志定义 (根据CH58x手册第25页)
#define RESET_FLAG_MASK         0x07    // 复位标志位掩码
#define RESET_FLAG_RPOR         0x01    // 上电复位 (Real Power-On Reset)
#define RESET_FLAG_GRWSM        0x05    // 下电模式唤醒复位 (Global Reset by Waking under Shutdown Mode)

// LED控制引脚定义 (PB12) - 用户要求使用PB12引脚
#define POWER_LED_PIN           GPIO_Pin_12

// 函数声明
uint8_t get_reset_flag(void);
bool is_power_on_reset(void);
bool is_shutdown_wakeup_reset(void);
void diagnose_reset_status(void);           // 复位状态诊断函数
void configure_wakeup_keys(void);
void enter_shutdown_mode(void);
void power_led_init(void);
void power_led_on(void);
void power_led_off(void);
void power_led_blink_once(void);
void power_led_power_on_indicate(void);   // 上电指示：亮0.5秒
void power_led_wakeup_indicate(void);     // 唤醒指示：闪烁两次
void power_led_test(void);                // LED测试函数
void test_sleep_wakeup_led(void);         // 测试睡眠唤醒LED指示功能

#ifdef __cplusplus
}
#endif

#endif /* __SLEEP_MANAGER_H */
