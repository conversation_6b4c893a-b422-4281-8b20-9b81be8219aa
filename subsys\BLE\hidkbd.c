/********************************** (C) COPYRIGHT *******************************
 * File Name          : hidkbd.c
 * Author             : WCH
 * Version            : V1.0
 * Date               : 2018/12/10
 * Description        : ��������Ӧ�ó��򣬳�ʼ���㲥���Ӳ�����Ȼ��㲥��ֱ�����������󣬶�ʱ�ϴ���ֵ
 *******************************************************************************/

/*********************************************************************
 * INCLUDES
 */
#include "CONFIG.h"
#include "soc.h"
#include "devinfoservice.h"
#include "battservice.h"
#include "hidkbdservice.h"
#include "hiddev.h"
#include "hidkbd.h"
#include "HAL.h"
#include "RingBuffer/lwrb.h"
#include "USB/usbuser.h"
#include "PM/pm_task.h"
#include "device_config.h"
#include "backlight/backlight.h"
/*********************************************************************
 * MACROS
 */
// HID keyboard input report length
#define HID_KEYBOARD_IN_RPT_LEN     8

//
uint8_t led_flash;
extern uint8_t ideflag;
uint8 led5scnt=0;
uint8 waitcnt=0;
uint8 iled=1;
 //



// HID LED output report length
#define HID_LED_OUT_RPT_LEN         1

/*********************************************************************
 * CONSTANTS
 */
// Param update delay
#define START_PARAM_UPDATE_EVT_DELAY          3200

// Param update delay
#define START_PHY_UPDATE_DELAY                1600

// HID idle timeout in msec; set to zero to disable timeout
#define DEFAULT_HID_IDLE_TIMEOUT              60000

// Minimum connection interval (units of 1.25ms)
#define DEFAULT_DESIRED_MIN_CONN_INTERVAL     8

// Maximum connection interval (units of 1.25ms)
#define DEFAULT_DESIRED_MAX_CONN_INTERVAL     15

// Low energy connection interval (units of 1.25ms)
#define LE_DESIRED_MIN_CONN_INTERVAL            40

// Low energy connection interval (units of 1.25ms)
#define LE_DESIRED_MAX_CONN_INTERVAL            80

// Slave latency to use if parameter update request
#define DEFAULT_DESIRED_SLAVE_LATENCY         5

// Supervision timeout value (units of 10ms)
#define DEFAULT_DESIRED_CONN_TIMEOUT          300

// Default passcode
#define DEFAULT_PASSCODE                      0

// Default GAP pairing mode
#define DEFAULT_PAIRING_MODE                  GAPBOND_PAIRING_MODE_WAIT_FOR_REQ

// Default MITM mode (TRUE to require passcode or OOB when pairing)
#define DEFAULT_MITM_MODE                     FALSE

// Default bonding mode, TRUE to bond
#define DEFAULT_BONDING_MODE                  TRUE

// Default GAP bonding I/O capabilities
#define DEFAULT_IO_CAPABILITIES               GAPBOND_IO_CAP_NO_INPUT_NO_OUTPUT

// Battery level is critical when it is less than this %
#define DEFAULT_BATT_CRITICAL_LEVEL          99

/*********************************************************************
 * TYPEDEFS
 */

/*********************************************************************
 * GLOBAL VARIABLES
 */

// Task ID
uint8 hidEmuTaskId = INVALID_TASK_ID;

size_t hidEmu_Mtu = 23;

uint8_t devAddr[6];

uint8_t devAddrType;

static bool terminate_flag = false;

/*********************************************************************
 * EXTERNAL VARIABLES
 */

/*********************************************************************
 * EXTERNAL FUNCTIONS
 */

/*********************************************************************
 * LOCAL VARIABLES
 */
static uint8_t advertData[] = {
    0x02,
    GAP_ADTYPE_FLAGS,
    GAP_ADTYPE_FLAGS_LIMITED | GAP_ADTYPE_FLAGS_BREDR_NOT_SUPPORTED,
};

static uint8_t scanRspData[] = {
    0x02,// length of this data
    GAP_ADTYPE_POWER_LEVEL,
//    0xFF,
    0,       // 0dBm

    0x03,
    GAP_ADTYPE_APPEARANCE,
    LO_UINT16(GAP_APPEARE_HID_KEYBOARD),
    HI_UINT16(GAP_APPEARE_HID_KEYBOARD),

    0x03,
    GAP_ADTYPE_16BIT_COMPLETE,
    LO_UINT16(HID_SERV_UUID),
    HI_UINT16(HID_SERV_UUID),

    0x07,
    GAP_ADTYPE_LOCAL_NAME_COMPLETE,
    0xE4,0xBA,0x91,0xE9,0x94,0xAE
//    'Y', 'U', 'N', 'K' , 'E' , 'Y'
};

// Device name attribute value
//static  uint8 attDeviceName[GAP_DEVICE_NAME_LEN] = "YUNKEY";
static  uint8 attDeviceName[GAP_DEVICE_NAME_LEN] = {0xE4,0xBA,0x91,0xE9,0x94,0xAE};
// HID Dev configuration
static hidDevCfg_t hidEmuCfg = {
DEFAULT_HID_IDLE_TIMEOUT,   // Idle timeout
        HID_FEATURE_FLAGS           // HID feature flags
        };

uint16 hidEmuConnHandle = GAP_CONNHANDLE_INIT;

/*********************************************************************
 * LOCAL FUNCTIONS
 */

static void hidEmu_ProcessTMOSMsg(tmos_event_hdr_t *pMsg);
static uint8 hidEmuRcvReport(uint8 len, uint8 *pData);
static uint8 hidEmuRptCB(uint8 id, uint8 type, uint16 uuid, uint8 oper,
        uint16 *pLen, uint8 *pData);
static void hidEmuEvtCB(uint8 evt);
static void hidEmuStateCB(gapRole_States_t newState, gapRoleEvent_t * pEvent);

/*********************************************************************
 * PROFILE CALLBACKS
 */

static hidDevCB_t hidEmuHidCBs = { hidEmuRptCB, hidEmuEvtCB,
NULL, hidEmuStateCB };

/*********************************************************************
 * PUBLIC FUNCTIONS
 */


static inline void bt_adv_direct(uint8_t addr_type, uint8_t *addr)
{
    uint8_t adv_event_type;
    adv_event_type = GAP_ADTYPE_ADV_HDC_DIRECT_IND;


    uint8_t Adv_Direct_Addr[B_ADDR_LEN];
    uint8_t Adv_Direct_Type = (addr_type&0x03);

    tmos_memcpy(Adv_Direct_Addr, addr,
            sizeof(Adv_Direct_Addr));

    PRINT("Adv Direct type %#x (", Adv_Direct_Type);
    for (int i = 0; i < 6; i++) {
        if(i) PRINT(" ");
        PRINT("%#x", Adv_Direct_Addr[i]);
    }
    PRINT(")\n");



    GAPRole_SetParameter( GAPROLE_ADV_DIRECT_ADDR, sizeof(Adv_Direct_Addr),
            Adv_Direct_Addr);
    GAPRole_SetParameter( GAPROLE_ADV_DIRECT_TYPE, sizeof(Adv_Direct_Type),
            &Adv_Direct_Type);
//    GAPRole_SetParameter( GAPROLE_ADV_EVENT_TYPE, sizeof(adv_event_type),
//            &adv_event_type);
}


uint8_t direct_count = 0;
static uint8_t fast_adv_mode = 1;  // 快速连接模式标志

bool bt_adv_data_init(void) {
    PRINT("=== BLE Advertising Initialization ===\n");

    // Setup the GAP Peripheral Role Profile
    uint8 initial_advertising_enable = TRUE;
    uint16 advInt;

    // 根据模式设置广播间隔
    if (fast_adv_mode) {
        advInt = 32;  // 快速模式: 20ms (32 * 0.625ms)
        PRINT("🚀 Fast advertising mode: %d (%.1f ms)\n", advInt, advInt * 0.625);
    } else {
        advInt = 160; // 一般模式: 100ms (160 * 0.625ms)
        PRINT("🐌 Normal advertising mode: %d (%.1f ms)\n", advInt, advInt * 0.625);
    }

    GAP_SetParamValue(TGAP_DISC_ADV_INT_MIN, advInt);
    GAP_SetParamValue(TGAP_DISC_ADV_INT_MAX, advInt);

    uint8_t ID_Num = device_bond.ID_Num;


    if (device_bond.ID[ID_Num].isbond) {

        GAP_SetParamValue(TGAP_DISC_ADV_INT_MIN, advInt);
        GAP_SetParamValue(TGAP_DISC_ADV_INT_MAX, advInt);

        PRINT("type = %d\n",device_bond.ID[ID_Num].remote_addr_type);

//         if(device_bond.ID[ID_Num].remote_addr_type == ADDRTYPE_PUBLIC) {

        uint8_t enable = ENABLE;


        if(device_bond.ID[ID_Num].remote_addr_type&0x30){
            GAPBondMgr_SetParameter( GAPBOND_AUTO_SYNC_RL, sizeof(uint8), &enable);
        }
        else{
            enable = DISABLE;
            GAPBondMgr_SetParameter( GAPBOND_AUTO_SYNC_RL, sizeof(uint8), &enable);
        }

        if(direct_count < 2)
        {
         bt_adv_direct(device_bond.ID[ID_Num].remote_addr_type,
                 device_bond.ID[ID_Num].remote_addr);
        }else{
        uint8_t adv_event_type;
        adv_event_type = GAP_ADTYPE_ADV_IND;
        GAPRole_SetParameter( GAPROLE_ADV_EVENT_TYPE, sizeof(adv_event_type),
                &adv_event_type);

        uint8_t policy = GAP_FILTER_POLICY_WHITE;
        uint8_t ret = GAPRole_SetParameter(GAPROLE_ADV_FILTER_POLICY, sizeof(policy),
            &policy);
        }
    }
    else{
//        scanRspData[16] = '0' + ID_Num;
//        scanRspData[18] = '0' + ID_Num;
//        attDeviceName[3] = '0' + ID_Num;
//        attDeviceName[5] = '0' + ID_Num;

        GAPRole_SetParameter( GAPROLE_ADVERT_DATA, sizeof(advertData), advertData);
        GAPRole_SetParameter( GAPROLE_SCAN_RSP_DATA, sizeof(scanRspData),
                scanRspData);
    }
    direct_count++;

    // Set the GAP Role Parameters
    GAPRole_SetParameter( GAPROLE_ADVERT_ENABLED, sizeof(uint8_t),
            &initial_advertising_enable);
    return true;
}


//�رչ㲥
bool bt_adv_data_stop(void) {

  uint8 initial_advertising_enable =FALSE;
  GAPRole_SetParameter( GAPROLE_ADVERT_ENABLED, sizeof(uint8_t),
          &initial_advertising_enable);

  return true;
}


void bt_bond_init(void) {
    // Setup the GAP Bond Manager
    uint32 passkey = DEFAULT_PASSCODE;
    uint8 pairMode = DEFAULT_PAIRING_MODE;
    uint8 mitm = DEFAULT_MITM_MODE;
    uint8 ioCap = DEFAULT_IO_CAPABILITIES;
    uint8 bonding = DEFAULT_BONDING_MODE;
    uint8 erase = ENABLE;


    GAPBondMgr_SetParameter( GAPBOND_PERI_DEFAULT_PASSCODE, sizeof(uint32),
            &passkey);
    GAPBondMgr_SetParameter( GAPBOND_PERI_PAIRING_MODE, sizeof(uint8),
            &pairMode);
    GAPBondMgr_SetParameter( GAPBOND_PERI_MITM_PROTECTION, sizeof(uint8),
            &mitm);
    GAPBondMgr_SetParameter( GAPBOND_PERI_IO_CAPABILITIES, sizeof(uint8),
            &ioCap);
    GAPBondMgr_SetParameter( GAPBOND_PERI_BONDING_ENABLED, sizeof(uint8),
            &bonding);
    GAPBondMgr_SetParameter( GAPBOND_ERASE_AUTO, sizeof(uint8), &erase);
}


/*********************************************************************
 * @fn      HidEmu_Init
 *
 * @brief   Initialization function for the HidEmuKbd App Task.
 *          This is called during initialization and should contain
 *          any application specific initialization (ie. hardware
 *          initialization/setup, table initialization, power up
 *          notificaiton ... ).
 *
 * @param   task_id - the ID assigned by TMOS.  This ID should be
 *                    used to send messages and set timers.
 *
 * @return  none
 */
void HidEmu_Init() {
    hidEmuTaskId = TMOS_ProcessEventRegister(HidEmu_ProcessEvent);

    direct_count = 0;
    bt_adv_data_init();

    // Set the GAP Characteristics
    GGS_SetParameter( GGS_DEVICE_NAME_ATT, GAP_DEVICE_NAME_LEN,
            (void *) attDeviceName);

    bt_bond_init();

    // Setup Battery Characteristic Values
    {
        uint8 critical = DEFAULT_BATT_CRITICAL_LEVEL;
        Batt_SetParameter( BATT_PARAM_CRITICAL_LEVEL, sizeof(uint8), &critical);
    }

    conn_params.interval_current = 0;
    conn_params.interval_max = DEFAULT_DESIRED_MAX_CONN_INTERVAL;
    conn_params.interval_min = DEFAULT_DESIRED_MIN_CONN_INTERVAL;
    conn_params.latency = DEFAULT_DESIRED_SLAVE_LATENCY;
    conn_params.timeout = DEFAULT_DESIRED_CONN_TIMEOUT;

    // Set up HID keyboard service
    Hid_AddService();

    // Register for HID Dev callback
    HidDev_Register(&hidEmuCfg, &hidEmuHidCBs);

    // Setup a delayed profile startup
    tmos_set_event(hidEmuTaskId, START_DEVICE_EVT);
}

void HidEmu_Unit(void)
{
    uint8 initial_advertising = FALSE;

    // Set the GAP Role Parameters
    GAPRole_SetParameter( GAPROLE_ADVERT_ENABLED, sizeof(uint8_t),
            &initial_advertising);


    hidEmuTaskId = 0;
}

/*********************************************************************
 * @fn      HidEmu_ProcessEvent
 *
 * @brief   HidEmuKbd Application Task event processor.  This function
 *          is called to process all events for the task.  Events
 *          include timers, messages and any other user defined events.
 *
 * @param   task_id  - The TMOS assigned task ID.
 * @param   events - events to process.  This is a bit map and can
 *                   contain more than one event.
 *
 * @return  events not processed
 */
uint16 HidEmu_ProcessEvent(uint8 task_id, uint16 events)
{
//    PRINT("HidEmu_ProcessEvent\n");
    if (events & SYS_EVENT_MSG) {
        uint8 *pMsg;

        if ((pMsg = tmos_msg_receive(hidEmuTaskId)) != NULL) {
            hidEmu_ProcessTMOSMsg((tmos_event_hdr_t *) pMsg);

            // Release the TMOS message
            tmos_msg_deallocate(pMsg);
        }

        // return unprocessed events
        return (events ^ SYS_EVENT_MSG);
    }

    if (events & START_DEVICE_EVT) {
        return (events ^ START_DEVICE_EVT);
    }

    if(events & STOP_ADV_EVT)
    {
        bt_adv_data_stop();
        pm_goto_standby();
        PRINT( "STOP_ADV_EVT\n");
        return (events ^ STOP_ADV_EVT);
    }


    if (events & START_PARAM_UPDATE_EVT) {
        // Send connect param update request

        static uint8_t retry = 0;

        if (hidEmuConnHandle == GAP_CONNHANDLE_INIT) {
            return (events ^ START_PARAM_UPDATE_EVT);
        }

//         PRINT("current interval: %#x\n", conn_params.interval_current);
//         PRINT("min interval: %#x\n", conn_params.interval_min);
//         PRINT("max interval: %#x\n", conn_params.interval_max);
        if(conn_params.interval_current >= conn_params.interval_min )
        if(conn_params.interval_current <= conn_params.interval_max){
            retry = 0;

            return (events ^ START_PARAM_UPDATE_EVT);
        }

        retry++;
        if(retry < 5){
            tmos_start_task(hidEmuTaskId, START_PARAM_UPDATE_EVT, MS1_TO_SYSTEM_TIME(1000));
        } else {
            retry = 0;
        }
//
        PRINT("conn param updata: %#x, %#x, %#x, %#x\n",conn_params.interval_min,
                    conn_params.interval_max,
                    conn_params.latency,
                    conn_params.timeout);

        GAPRole_PeripheralConnParamUpdateReq(hidEmuConnHandle,
            conn_params.interval_min,
            conn_params.interval_max,
            conn_params.latency,
            conn_params.timeout, hidEmuTaskId);

        return (events ^ START_PARAM_UPDATE_EVT);
    }

    if (events & START_PHY_UPDATE_EVT) {
        // start phy update
        PRINT("Send Phy Update %x...\n",
                GAPRole_UpdatePHY( hidEmuConnHandle, 0, GAP_PHY_BIT_LE_2M, GAP_PHY_BIT_LE_2M, 0 ));

        return (events ^ START_PHY_UPDATE_EVT);
    }

    if(events & OPEN_NOTE_EVT){
        extern void OpenHotiChannel();
        OpenHotiChannel();
        return (events ^ OPEN_NOTE_EVT);
    }


    // 注释掉LED闪烁事件处理 - 只保留电源LED功能
    /*
    if(events & HAL_LEDFLASH_EVENT)
    {

        if(led_flash==1)
        {
            iled=!iled;
            set_led_scr(iled);
//            PRINT( "HAL_LEDFLASH_EVENT\n");
            tmos_start_task( hidEmuTaskId, HAL_LEDFLASH_EVENT, MS1_TO_SYSTEM_TIME(1*1000));
        }

        return (events ^ HAL_LEDFLASH_EVENT);
       }
    */


    // 注释掉LED定时事件处理 - 只保留电源LED功能
    /*
    if(events & HAL_LED5s_EVENT)
    {
        led5scnt++;
        PRINT( "led5scnt: %x ...\n", led5scnt );
        if(led5scnt==1)
        {
            PRINT( "led5scnt: %x ...\n", led5scnt );
        set_led_scr(1);//1Ϊ����
        tmos_start_task( hidEmuTaskId, HAL_LED5s_EVENT, MS1_TO_SYSTEM_TIME(3*1000));
        }
        if(led5scnt==2)
        {

            set_led_scr(0);
            led5scnt=0;
        }
        return (events ^ HAL_LED5s_EVENT);
       }
    */

    if(events & HAL_WAITOUT1_EVENT)
    {
        waitcnt++;
        if(waitcnt==1)
        {
        tmos_start_task( hidEmuTaskId, HAL_WAITOUT1_EVENT, MS1_TO_SYSTEM_TIME(120*1000));
        }
        if(waitcnt==2)
        {

            waitcnt=0;
//            set_led_scr(0);
            pm_goto_standby();
        }
//        else {
//            waitcnt=0;
//        }
        return (events ^ HAL_LED5s_EVENT);
       }

    //HID_RPT_ID_KEY_IN:[0 0 0x15 0 0 0 0 0]�Ż��ѡ�

    if (events & BLE_SEND_DATA_EVT) {
        PRINT("BLE_SEND_DATA_EVT\n");
        uint8_t report_id;
        uint8_t keyVal[16] = { 0 };
        uint8_t ret;
        if(ideflag==1)//˯��ʱִ��
        { PRINT("ideflag==1\n");
            if (lwrb_get_full(&KEY_buff))                //����ַ��ߴ粿λ��Ϊ0�����������¡�
          {
            lwrb_peek(&KEY_buff, 0, &report_id, 1); //��ȡreport_id
            PRINT("getkeybuff%x\n\r",report_id);
            if (report_id == KEYNORMAL_ID)          //report_id=0,��������
            {

                lwrb_peek(&KEY_buff, 1, keyVal, 8);
                PRINT("keyVal[2]=%x\n\r",keyVal[2]);
                if(keyVal[2]==0x15)
                {PRINT("keyVal[2]==0x15?\n");
                  ideflag=0;
                  // 注释掉特殊按键LED控制 - 只保留电源LED功能
                  // set_led_scr(1);//1Ϊ����
                  pm_start_working((1000*600), (1000*6*600));
                  // tmos_start_task( hidEmuTaskId, HAL_LED5s_EVENT, MS1_TO_SYSTEM_TIME(3*1000));
                  tmos_start_task(hidEmuTaskId, BLE_SEND_DATA_EVT,MS1_TO_SYSTEM_TIME(10));
                }
                else
                {
                    pm_start_working((1000*2), (1000*6*600));
                }
                lwrb_skip(&KEY_buff, 8 + 1);
             }
            else
                {
                  pm_start_working((1000*2), (1000*6*600));
                }
          }
        }
     if(ideflag==0)//����ʱִ��
     {
         PRINT("ideflag==0\n");
        if (lwrb_get_full(&KEY_buff))                //����ַ��ߴ粿λ��Ϊ0�����������¡�
        {
            lwrb_peek(&KEY_buff, 0, &report_id, 1); //��ȡreport_id

            if (report_id == KEYNORMAL_ID)          //report_id=0,��������
            {
                lwrb_peek(&KEY_buff, 1, keyVal, 8);


                PRINT("HID_RPT_ID_KEY_IN:[");
                for (int i = 0; i < 8; i++) {
                    if(i) PRINT(" ");
                    PRINT("%#x", keyVal[i]);
                }
                PRINT("]\n");

                ret = HidDev_Report( HID_RPT_ID_KEY_IN, HID_REPORT_TYPE_INPUT,
                        8, keyVal);

                PRINT("Status:%x\n\r",ret);

                if (ret == 0) {
                    lwrb_skip(&KEY_buff, 8 + 1);
                }

            }

            else if (report_id == KEYBIT_ID)       //��ȡreport_id=1,��������
            {
                lwrb_peek(&KEY_buff, 1, keyVal, 16);
                PRINT("HID_R_KEYBIT_IN:[");
                for (int i = 0; i < 15; i++) {
                    if(i) PRINT(" ");
                    PRINT("%#x", keyVal[i]);
                }
                PRINT("]\n");
                ret = HidDev_Report( HID_RPT_ID_KEYBIT_IN, HID_REPORT_TYPE_INPUT,
                        15, &keyVal[1]);
                PRINT("Status:%x\n\r",ret);
                if (ret == 0) {
                    lwrb_skip(&KEY_buff, 16 + 1);
                }

            }

            else if(report_id == SYS_ID)           //report_id=2,��������
            {
                lwrb_peek(&KEY_buff, 1, keyVal, 2);
                PRINT("HID_RPT_ID_SYS_IN:[");
                for (int i = 0; i < 2; i++) {
                    if(i) PRINT(" ");
                    PRINT("%#x", keyVal[i]);
                }
                PRINT("]\n");

                ret = HidDev_Report( HID_RPT_ID_SYS_IN, HID_REPORT_TYPE_INPUT,
                        2, keyVal);
                PRINT("Status:%x\n\r",ret);
                if (ret == 0) {
                    lwrb_skip(&KEY_buff, 2 + 1);
                }

            }


            else if (report_id == CONSUME_ID)        //report_id=3,��������
            {
                lwrb_peek(&KEY_buff, 1, keyVal, 2);
                PRINT("HID_RPT_ID_KEYCONSUME_IN:[");
                for (int i = 0; i < 2; i++) {
                    if(i) PRINT(" ");
                    PRINT("%#x", keyVal[i]);
                }
                PRINT("]\n");
                ret = HidDev_Report( HID_RPT_ID_KEYCONSUME_IN, HID_REPORT_TYPE_INPUT,
                        2, keyVal);
                PRINT("Status:%x\n\r",ret);
                //if (ret == 0) {
                    lwrb_skip(&KEY_buff, 2 + 1);
                //}

            }

            else if (report_id == VENDOR_ID)            //report_id=5,��������
            {

            }

            else if (report_id == MOUSE_ID)
            {           //report_id=5,��������
                lwrb_peek(&KEY_buff, 1, keyVal, 4);
                PRINT("HID_RPT_ID_MOUSE_IN:[");
                for (int i = 0; i < 2; i++) {
                    if(i) PRINT(" ");
                    PRINT("%#x", keyVal[i]);
                }
                PRINT("]\n");

                ret = HidDev_Report( HID_RPT_ID_MOUSE_IN, HID_REPORT_TYPE_INPUT,
                        4, keyVal);PRINT("Status:%x\n\r",ret);
                if (ret == 0) {
                    lwrb_skip(&KEY_buff, 2 + 1);
                }

            }

            else
            {                                      //report_id����������,��������
                LOG_DEBUG("report id error:%d", report_id);
            }

            if (lwrb_get_full(&KEY_buff))               //��������ַ��ߴ粿λ��Ϊ0������ʱ10ms�ٴ����С�
            {
                tmos_start_task(hidEmuTaskId, BLE_SEND_DATA_EVT,
                        MS1_TO_SYSTEM_TIME(10));  //FIXME�����ݷ���ֵ�ж��Ƿ��ٴη���
            }
        }
       }
        return (events ^ BLE_SEND_DATA_EVT);
    }
    return 0;
}

/*********************************************************************
 * @fn      hidEmu_ProcessTMOSMsg
 *
 * @brief   Process an incoming task message.
 *
 * @param   pMsg - message to process
 *
 * @return  none
 */
static void hidEmu_ProcessTMOSMsg(tmos_event_hdr_t *pMsg) {
//    PRINT("hidEmu_ProcessTMOSMsg\n");
    switch (pMsg->event) {
    case GATT_MSG_EVENT: {
//            PRINT("GATT_MSG_EVENT\n");
        gattMsgEvent_t *msg = (gattMsgEvent_t *) pMsg;
        if (msg->method == ATT_MTU_UPDATED_EVENT) {
            hidEmu_Mtu = msg->msg.mtuEvt.MTU;
            LOG_INFO("Mtu:%d\n", msg->msg.mtuEvt.MTU);
        }
        break;
    }

    case KEY_MESSAGE: {
//        PRINT("KEY_MESSAGE\n");
        if (hidEmuConnHandle == GAP_CONNHANDLE_INIT) {
            lwrb_reset(&KEY_buff);
//            PRINT("KEY_MESSAGE1\n");
            break;
        }
        SendMSG_t *msg = (SendMSG_t *) pMsg;

        if(msg->hdr.status){
            tmos_set_event(hidEmuTaskId, BLE_SEND_DATA_EVT);
            tmos_set_event(hidEmuTaskId, START_PARAM_UPDATE_EVT);
//            PRINT("KEY_MESSAGE2\n");
        }

        break;
    }


    case RF_MS_STATE_CHANGE: {
//        PRINT("RF_MS_STATE_CHANGE\n");
        switch (pMsg->status)
        {
        case PM_STATE_SLEEP:
//            PRINT("RF_MS_STATE_CHANGE1\n");
            conn_params.interval_max = LE_DESIRED_MAX_CONN_INTERVAL;
            conn_params.interval_min = LE_DESIRED_MIN_CONN_INTERVAL;
            break;

        case PM_STATE_ACTIVE:
//            PRINT("RF_MS_STATE_CHANGE2\n");
            conn_params.interval_max = DEFAULT_DESIRED_MAX_CONN_INTERVAL;
            conn_params.interval_min = DEFAULT_DESIRED_MIN_CONN_INTERVAL;
            break;

        default:
            break;
        }

        if(hidEmuConnHandle != GAP_CONNHANDLE_INIT) {
            tmos_start_task(hidEmuTaskId, START_PARAM_UPDATE_EVT, MS1_TO_SYSTEM_TIME(100));
        }

        break;
    }

    default:
        break;
    }
}

/*********************************************************************
 * @fn      hidEmuStateCB
 *
 * @brief   GAP state change callback.
 *
 * @param   newState - new state
 *
 * @return  none
 */

static void hidEmuStateCB(gapRole_States_t newState, gapRoleEvent_t * pEvent) {
    PRINT("hidEmuStateCB1\n");
    switch (newState & GAPROLE_STATE_ADV_MASK) {
//    PRINT("hidEmuStateCB2\n");
    case GAPROLE_STARTED: {
//        uint8 ownAddr[6];
//        GAPRole_GetParameter( GAPROLE_BD_ADDR, ownAddr);
//        GAP_ConfigDeviceAddr( ADDRTYPE_STATIC, ownAddr);
        hidEmuConnHandle = GAP_CONNHANDLE_INIT;
        PRINT("Initialized..\n");
    }
        break;

    case GAPROLE_ADVERTISING:
        if (pEvent->gap.opcode == GAP_MAKE_DISCOVERABLE_DONE_EVENT) {
            PRINT("Advertising..\n");
            // 注释掉蓝牙广播状态LED控制 - 只保留电源LED功能
            // led_flash=1;
            // tmos_start_task( hidEmuTaskId, HAL_LEDFLASH_EVENT, MS1_TO_SYSTEM_TIME(1*1000));
            tmos_start_task( hidEmuTaskId, HAL_WAITOUT1_EVENT, MS1_TO_SYSTEM_TIME(120*1000));
        }
        break;

    case GAPROLE_CONNECTED:
        if (pEvent->gap.opcode == GAP_LINK_ESTABLISHED_EVENT) {
            gapEstLinkReqEvent_t *event = (gapEstLinkReqEvent_t *) pEvent;

            lwrb_reset(&KEY_buff);
            // get connection handle
            hidEmuConnHandle = event->connectionHandle;
            tmos_start_task(hidEmuTaskId, START_PARAM_UPDATE_EVT,START_PARAM_UPDATE_EVT_DELAY);
            tmos_start_task(hidEmuTaskId,OPEN_NOTE_EVT,1600);
            PRINT("✅ Connected successfully!\n");

            // 连接成功，重置为快速连接模式（为下次断开重连做准备）
            fast_adv_mode = 1;
            PRINT("🔄 Reset to fast advertising mode for next disconnection\n");

            // 注释掉蓝牙连接状态LED控制 - 只保留电源LED功能
            // led_flash=0;
            // set_led_scr(1);
            // tmos_start_task( hidEmuTaskId, HAL_LED5s_EVENT, MS1_TO_SYSTEM_TIME(3*1000));
            tmos_stop_task( hidEmuTaskId, HAL_WAITOUT1_EVENT );
            waitcnt=0;
            tmos_memcpy(devAddr, event->devAddr, 6);


            devAddrType = event->devAddrType;

            PRINT("conn addr type: %d (", devAddrType);
            for(int i = 0 ; i < 6; i++) {
                PRINT(" %#x", devAddr[i]);
            }
            PRINT(" )\n");
            terminate_flag = false;
        }
        break;

    case GAPROLE_WAITING:
        if (pEvent->gap.opcode == GAP_END_DISCOVERABLE_DONE_EVENT) {
            PRINT("Waiting for advertising..\n");
//            led_flash=0;
//            set_led_scr(0);//1Ϊ����
//            bt_adv_data_stop();
//            pm_goto_standby();
        } else if (pEvent->gap.opcode == GAP_LINK_TERMINATED_EVENT) {
            hidEmuConnHandle = GAP_CONNHANDLE_INIT;
            PRINT("🔌 Disconnected - Reason: 0x%02X\n", pEvent->linkTerminate.reason);
            PRINT("   Reason codes: 0x13=User terminated, 0x16=Connection timeout\n");

            // 重置为快速连接模式
            fast_adv_mode = 1;
            PRINT("🔄 Reset to fast advertising mode for reconnection\n");

            /* Prevent reconnection */
            if (terminate_flag) {
                terminate_flag = false;
                PRINT("⚠️  Terminate flag set - not restarting advertising\n");
                break;
            }
        } else if (pEvent->gap.opcode == GAP_LINK_ESTABLISHED_EVENT) {
            PRINT("⏰ Advertising timeout - no connection established\n");

            // 从快速模式切换到一般模式
            if (fast_adv_mode) {
                fast_adv_mode = 0;
                PRINT("🔄 Switching from fast to normal advertising mode\n");
            }
        }

        // Enable advertising
        PRINT("🔄 Restarting BLE advertising...\n");
        bt_adv_data_init();
        PRINT("📡 Advertising restarted - device should be discoverable again\n");
        break;

    case GAPROLE_ERROR:
        PRINT("Error %x ..\n", pEvent->gap.opcode);
        break;

    default:
        break;
    }
}
/*********************************************************************
 * @fn      hidEmuRcvReport
 *
 * @brief   Process an incoming HID keyboard report.
 *
 * @param   len - Length of report.
 * @param   pData - Report data.
 *
 * @return  status
 */
static uint8 hidEmuRcvReport(uint8 len, uint8 *pData) {
    // verify data length
    if (len == HID_LED_OUT_RPT_LEN) {
        // 注释掉HID LED输出报告处理 - 只保留电源LED功能
        // set LEDs
        // (pData[0] & 0x01) ? set_led_num(1) : set_led_num(0);
        // (pData[0] & 0x02) ? set_led_cap(1) : set_led_cap(0);
        // (pData[0] & 0x04) ? set_led_scr(1) : set_led_scr(0);
        PRINT("HID LED report received but ignored - only power LED enabled\n");
        return SUCCESS;
    } else {
        return ATT_ERR_INVALID_VALUE_SIZE;
    }
}

/*********************************************************************
 * @fn      hidEmuRptCB
 *
 * @brief   HID Dev report callback.
 *
 * @param   id - HID report ID.
 * @param   type - HID report type.
 * @param   uuid - attribute uuid.
 * @param   oper - operation:  read, write, etc.
 * @param   len - Length of report.
 * @param   pData - Report data.
 *
 * @return  GATT status code.
 */
static uint8 hidEmuRptCB(uint8 id, uint8 type, uint16 uuid, uint8 oper,
        uint16 *pLen, uint8 *pData) {
    uint8 status = SUCCESS;

    // write
    if (oper == HID_DEV_OPER_WRITE) {
        if (uuid == REPORT_UUID) {
            // process write to LED output report; ignore others
            if (type == HID_REPORT_TYPE_OUTPUT) {
                status = hidEmuRcvReport(*pLen, pData);
            }
        }

        if (status == SUCCESS) {
            status = Hid_SetParameter(id, type, uuid, *pLen, pData);
        }
    }
    // read
    else if (oper == HID_DEV_OPER_READ) {
        status = Hid_GetParameter(id, type, uuid, pLen, pData);
    }
    // notifications enabled
    else if (oper == HID_DEV_OPER_ENABLE) {
//          tmos_start_task( hidEmuTaskId, START_REPORT_EVT, 500 );
    }
    return status;
}

/*********************************************************************
 * @fn      hidEmuEvtCB
 *
 * @brief   HID Dev event callback.
 *
 * @param   evt - event ID.
 *
 * @return  HID response code.
 */
static void hidEmuEvtCB(uint8 evt) {
    // process enter/exit suspend or enter/exit boot mode
    return;
}

void Disconnet_conn(void)
{
    if (GAPRole_TerminateLink(hidEmuConnHandle) == SUCCESS) {
        PRINT("disconnet: %d success\n", hidEmuConnHandle);
        terminate_flag = true;
    }
}
/*********************************************************************
 *********************************************************************/
