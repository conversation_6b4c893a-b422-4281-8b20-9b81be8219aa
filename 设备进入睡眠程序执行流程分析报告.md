# 设备进入睡眠程序执行流程分析报告

## 概述
本文档详细分析了设备进入睡眠模式时执行的完整程序流程，包括触发条件、执行路径、关键函数和最终进入睡眠的底层实现。

## 完整执行流程

### **1. 触发条件**
设备进入睡眠的触发条件是：**1分钟无按键操作**

#### **触发机制：**
```c
// 在 subsys/PM/pm_task.h 中定义
#define PM_WORKING_TIMEOUT (1000*60)    // 1分钟无按键进入下电模式

// 在 subsys/PM/pm_task.c 中初始化
pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
```

#### **计时器管理：**
```c
// 在 subsys/PM/pm_task.c 的 pm_start_working() 中
tmos_start_task(pm_task_id, PM_ENTER_IDLE_EVENT, MS1_TO_SYSTEM_TIME(working_timeout));
```

### **2. 事件触发流程**

#### **步骤1：TMOS事件触发**
当1分钟超时到达时，TMOS系统会触发 `PM_ENTER_IDLE_EVENT` 事件。

#### **步骤2：事件处理**
在 `pm_event()` 函数中处理事件：
```c
if(events & PM_ENTER_IDLE_EVENT) {
    PM_DBG("1 minute timeout - entering powerdown mode\n");
    
    // 1分钟无按键操作，直接进入深度下电模式
    pm_goto_powerdown();
    
    return (events ^ PM_ENTER_IDLE_EVENT);
}
```

### **3. 主要执行函数**

#### **函数1：pm_goto_powerdown()**
**位置：** `subsys/PM/pm_task.c` 第120-164行

**执行内容：**
```c
void pm_goto_powerdown(void)
{
    PM_DBG("Enter PM powerdown mode!\n");
    
    // 设置睡眠状态
    pm_set_sleep_state(true);
    
    DelayMs(10);

    // 断开所有连接
    if(device_mode == MODE_BLE) {
        // 断开BLE连接
        GAPRole_TerminateLink(GAP_CONNHANDLE_ALL);
        DelayMs(100);  // 等待断开完成
    }

    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);

    // 配置大部分GPIO为下拉输入以降低功耗
    GPIOA_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All, GPIO_ModeIN_PD);

    // 配置E、Q、R三个唤醒按键为上拉输入，并启用中断
    // E键 (PA0)
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);
    PFIC_EnableIRQ(GPIO_A_IRQn);

    // Q键 (PB8) 和 R键 (PB9)
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);
    GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge);
    PFIC_EnableIRQ(GPIO_B_IRQn);

    PM_DBG("Configured wakeup pins: PA0(E), PB8(Q), PB9(R)\n");
    DelayMs(2);

    // 进入深度睡眠模式
    LowPower_Shutdown(0); // 全部断电，唤醒后复位
}
```

#### **函数2：enter_shutdown_mode()**
**位置：** `drivers/power_led/sleep_manager.c` 第170-227行

**注意：** 这个函数目前**没有被调用**，但包含了完整的睡眠配置逻辑。

**执行内容：**
```c
void enter_shutdown_mode(void)
{
    PRINT("\n=== ENTERING SHUTDOWN MODE ===\n");
    PRINT("Preparing to enter shutdown mode...\n");

    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);

    // 配置大部分GPIO为下拉输入以降低功耗，但排除唤醒按键和LED引脚
    GPIOA_ModeCfg(GPIO_Pin_All & ~GPIO_Pin_0, GPIO_ModeIN_PD);
    GPIOB_ModeCfg(GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_12), GPIO_ModeIN_PD);

    // 配置LED引脚为推挽输出
    GPIOB_ModeCfg(GPIO_Pin_12, GPIO_ModeOut_PP_5mA);
    GPIOB_SetBits(GPIO_Pin_12);

    // 配置唤醒按键
    configure_wakeup_keys();

    DelayMs(50);  // 确保所有配置生效

    PRINT("*** SYSTEM ENTERING SLEEP MODE ***\n");
    PRINT("*** Press E/Q/R keys to wake up ***\n");
    DelayMs(100);  // 确保串口输出完成

    // 进入下电模式，保持RAM以便调试
    LowPower_Shutdown(0);
    
    // 如果执行到这里，说明进入睡眠失败
    PRINT("ERROR: Failed to enter shutdown mode!\n");
    while(1) {
        DelayMs(1000);
        PRINT("Sleep failed, retrying...\n");
    }
}
```

### **4. 最终进入睡眠的底层函数**

#### **核心函数：LowPower_Shutdown(0)**
**位置：** `StdPeriphDriver/CH58x_pwr.c` 第378-414行

**函数签名：**
```c
__HIGH_CODE
void LowPower_Shutdown(uint8_t rm)
```

**参数说明：**
- `rm = 0`：不保留任何RAM，全部断电

**执行流程：**
```c
void LowPower_Shutdown(uint8_t rm)
{
    uint8_t x32Kpw, x32Mpw;

    // 1. 软件复位准备
    FLASH_ROM_SW_RESET();
    
    // 2. 保存时钟调谐参数
    x32Kpw = R8_XT32K_TUNE;
    x32Mpw = R8_XT32M_TUNE;
    x32Mpw = (x32Mpw & 0xfc) | 0x03; // 150%驱动强度
    
    // 3. 根据RTC计数调整32K时钟
    if(R16_RTC_CNT_32K > 0x3fff) {
        x32Kpw = (x32Kpw & 0xfc) | 0x01; // LSE时钟降低到最低驱动强度
    }

    // 4. 配置系统进入深度睡眠
    sys_safe_access_enable();
    R8_BAT_DET_CTRL = 0; // 关闭电压检测
    sys_safe_access_enable();
    R8_XT32K_TUNE = x32Kpw;
    R8_XT32M_TUNE = x32Mpw;
    sys_safe_access_disable();
    
    // 5. 设置系统时钟为6.4MHz
    SetSysClock(CLK_SOURCE_HSE_6_4MHz);

    // 6. 启用深度睡眠模式
    PFIC->SCTLR |= (1 << 2); //deep sleep

    // 7. 配置电源计划
    sys_safe_access_enable();
    R8_SLP_POWER_CTRL |= RB_RAM_RET_LV;
    sys_safe_access_enable();
    R16_POWER_PLAN = RB_PWR_PLAN_EN | RB_PWR_MUST_0010 | rm;
    
    // 8. 等待中断（进入睡眠）
    __WFI();
    __nop();
    __nop();
    
    // 9. 睡眠后的处理（通常不会执行到这里）
    FLASH_ROM_SW_RESET();
    sys_safe_access_enable();
    R8_RST_WDOG_CTRL |= RB_SOFTWARE_RESET;
    sys_safe_access_disable();
}
```

### **5. 实际执行的程序路径**

#### **当前激活的路径：**
```
1分钟无操作 → PM_ENTER_IDLE_EVENT → pm_goto_powerdown() → LowPower_Shutdown(0)
```

#### **未激活的路径：**
```
enter_shutdown_mode() 函数（包含更详细的配置，但未被调用）
```

### **6. 关键配置说明**

#### **唤醒按键配置：**
- **E键 (PA0)**：上拉输入，下降沿触发中断
- **Q键 (PB8)**：上拉输入，下降沿触发中断  
- **R键 (PB9)**：上拉输入，下降沿触发中断

#### **GPIO配置：**
- 大部分GPIO配置为下拉输入，降低功耗
- 唤醒按键保持上拉输入
- LED引脚配置为推挽输出

#### **电源配置：**
- 关闭电压检测
- 调整时钟驱动强度
- 启用深度睡眠模式
- 配置电源计划

### **7. 睡眠模式特点**

#### **深度睡眠特性：**
- 系统时钟停止
- 大部分外设关闭
- 仅保留必要的唤醒源
- 唤醒后系统复位

#### **唤醒源：**
- GPIO中断（E、Q、R按键）
- 其他配置的唤醒源（如果有）

#### **功耗：**
- 极低功耗模式
- 仅保留必要的RAM和唤醒电路

## 总结

设备进入睡眠的完整程序执行流程为：

1. **触发**：1分钟无操作超时
2. **事件处理**：TMOS触发PM_ENTER_IDLE_EVENT
3. **主要执行**：pm_goto_powerdown()函数
4. **底层实现**：LowPower_Shutdown(0)函数
5. **进入睡眠**：__WFI()指令等待唤醒

**注意：** 虽然 `enter_shutdown_mode()` 函数包含了更完整的睡眠配置，但当前代码中并未被调用。主要的睡眠逻辑都在 `pm_goto_powerdown()` 函数中实现。
