# LED闪烁问题完整分析报告

## 问题描述
用户报告：上电后LED闪烁两次（而不是预期的亮0.5秒），设备无法通过按键唤醒。

## 根本原因分析

### 🔍 **引脚冲突问题**

通过全面检查代码，发现了关键的引脚冲突：

#### 1. 冲突的引脚定义
- **睡眠管理器**: `POWER_LED_PIN = GPIO_Pin_12` (PB12)
- **背光系统**: `LED_SCR = GPIO_Pin_12` (PB12)
- **背光系统**: `LED_CAP = GPIO_Pin_12` (PB12)

#### 2. 冲突的函数调用
在 `subsys/HAL/MCU.c` 第396-398行：
```c
#if (defined HAL_LED) && (HAL_LED == TRUE)
  bkinit();
#endif
```

在 `drivers/backlight/backligth.c` 第75-82行：
```c
void bkinit(void) {
    // ...
    GPIOA_ResetBits(LED_CAP);           // 控制PA12
    GPIOB_ResetBits(LED_SCR | LED_NUM); // 控制PB12和PB13
    GPIOA_ModeCfg(LED_CAP, GPIO_ModeOut_PP_5mA);
    GPIOB_ModeCfg( LED_SCR | LED_NUM, GPIO_ModeOut_PP_5mA);
    // ...
}
```

### 🔍 **执行时序分析**

#### 上电启动时的执行顺序：
1. **main()函数开始** → 基础初始化
2. **检测复位标志** → 0x01（上电复位）
3. **执行上电逻辑**：
   - `power_led_init()` → 配置PB12为输出
   - `power_led_power_on_indicate()` → LED亮0.5秒
   - `configure_wakeup_keys()` → 配置唤醒按键
   - `enter_shutdown_mode()` → 进入睡眠
4. **但是在正常初始化过程中**：
   - `bkinit()` 被调用 → 重新配置PB12引脚
   - 这导致LED状态异常，看起来像闪烁两次

### 🔍 **无法唤醒的原因**

#### 1. GPIO配置被覆盖
- 在main.c第148-151行，HAL_SLEEP相关的GPIO配置会覆盖唤醒按键配置
- 背光初始化也可能影响GPIO状态

#### 2. 中断配置不完整
- 之前的代码缺少完整的GPIO中断配置
- 缺少中断使能

## 修复方案

### ✅ **解决方案1：更换LED引脚**

将电源LED从PB12改为PB14，避免与背光系统冲突：

#### 修改文件1: `drivers/power_led/sleep_manager.h`
```c
// LED控制引脚定义 (PB14) - 避免与背光系统的PB12冲突
#define POWER_LED_PIN           GPIO_Pin_14
```

#### 修改文件2: `APP/src/main.c`
```c
// PB8、PB9是唤醒按键Q、R，PB14是LED引脚，需要特殊处理
GPIOB_ModeCfg( GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_14), GPIO_ModeIN_PD);

// 配置LED引脚为推挽输出并保持关闭状态
GPIOB_ModeCfg(GPIO_Pin_14, GPIO_ModeOut_PP_5mA);
GPIOB_ResetBits(GPIO_Pin_14);
```

#### 修改文件3: `drivers/power_led/sleep_manager.c`
```c
// PB8、PB9是唤醒按键，PB14是LED引脚，需要特殊处理
GPIOB_ModeCfg(GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_14), GPIO_ModeIN_PD);

// 配置LED引脚为推挽输出并保持关闭状态
GPIOB_ModeCfg(GPIO_Pin_14, GPIO_ModeOut_PP_5mA);
GPIOB_ResetBits(GPIO_Pin_14);
```

### ✅ **解决方案2：完善GPIO中断配置**

在 `drivers/power_led/sleep_manager.c` 中已经添加了：
- `GPIOA_ITModeCfg()` - GPIO中断模式配置
- `GPIOB_ITModeCfg()` - GPIO中断模式配置
- `PFIC_EnableIRQ()` - 中断使能

## 控制LED闪烁的所有地方

### 1. 睡眠管理器中的LED控制
**文件**: `drivers/power_led/sleep_manager.c`
- `power_led_init()` - 初始化LED引脚
- `power_led_on()` - 点亮LED
- `power_led_off()` - 关闭LED
- `power_led_power_on_indicate()` - 上电指示（亮0.5秒）
- `power_led_wakeup_indicate()` - 唤醒指示（闪烁两次）
- `power_led_blink_once()` - 通用闪烁一次

### 2. HAL LED系统
**文件**: `subsys/HAL/LED.c`
- `HAL_LedInit()` - HAL LED初始化
- `HalLedSet()` - 设置LED状态
- `HalLedBlink()` - LED闪烁控制
- `HalLedUpdate()` - LED状态更新
- `HalLedOnOff()` - LED开关控制

**注意**: HAL LED系统使用的是PA7引脚（LED1_BV = BV(7)），与我们的PB14不冲突。

### 3. 背光系统
**文件**: `drivers/backlight/backligth.c`
- `bkinit()` - 背光初始化（会配置PB12、PB13、PA12）
- `set_led_scr()` - 控制Scroll Lock LED (PB12)
- `set_led_cap()` - 控制Caps Lock LED (PA12)
- `set_led_num()` - 控制Num Lock LED (PB13)

**冲突**: 背光系统的LED_SCR和LED_CAP都使用PB12，与原来的POWER_LED_PIN冲突。

### 4. main.c中的LED调用
**文件**: `APP/src/main.c`
- 第98行: `power_led_init()` - 上电复位时初始化
- 第101行: `power_led_power_on_indicate()` - 上电指示
- 第119行: `power_led_init()` - 唤醒复位时初始化
- 第122行: `power_led_wakeup_indicate()` - 唤醒指示
- 第136行: `power_led_init()` - 其他复位时初始化

## 预期修复效果

### ✅ **解决LED闪烁两次问题**
- 电源LED改用PB14引脚，不再与背光系统冲突
- 上电时只会执行一次LED指示（亮0.5秒）
- 不会受到背光初始化的影响

### ✅ **解决无法唤醒问题**
- 完善了GPIO中断配置
- 正确配置了唤醒按键的上拉输入模式
- 避免了GPIO配置被覆盖

### ✅ **保持现有功能**
- 背光系统继续使用PB12、PB13、PA12
- HAL LED系统继续使用PA7
- 电源LED使用独立的PB14引脚

## 测试验证

### 1. 硬件连接
**重要**: 需要将电源LED从PB12改接到PB14引脚！

### 2. 功能测试
- **上电测试**: LED(PB14)应该亮0.5秒后熄灭
- **唤醒测试**: 按E/Q/R键，LED(PB14)应该闪烁两次
- **背光测试**: 确认键盘背光功能不受影响

### 3. 调试验证
通过串口日志确认：
- 复位标志检测正确
- GPIO配置过程正常
- 睡眠和唤醒流程正确

## 注意事项

1. **硬件修改**: 必须将LED连接从PB12改到PB14
2. **引脚冲突**: 确保PB14没有被其他功能使用
3. **功能验证**: 测试所有相关功能是否正常工作
4. **功耗测试**: 验证睡眠模式下的功耗是否符合预期

## 相关文件修改清单

- ✅ `drivers/power_led/sleep_manager.h` - 更改LED引脚定义
- ✅ `drivers/power_led/sleep_manager.c` - 更新GPIO配置和中断配置
- ✅ `APP/src/main.c` - 更新GPIO配置，排除新的LED引脚
