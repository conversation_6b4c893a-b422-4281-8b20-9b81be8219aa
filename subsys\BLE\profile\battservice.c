/********************************** (C) COPYRIGHT *******************************
* File Name          : battservice.c
* Author             : WCH
* Version            : V1.0
* Date               : 2018/12/10
* Description        : ��ط���
            
*******************************************************************************/

/*********************************************************************
 * INCLUDES
 */
#include "CONFIG.h"
#include "hiddev.h"
#include "battservice.h"
#include "CH58x_common.h"

/*********************************************************************
 * MACROS
 */

/*********************************************************************
 * CONSTANTS
 */

// ADC voltage levels - 根据锂电池实际电压-容量表格和分压电路重新定义

// 电池电压范围：2.5V - 4.2V
// 分压后电压范围：1.25V - 2.1V (两个1M电阻分压)
// ADC参考电压：1.05V
// ADC值范围：约2381 - 4096

#define BATT_ADC_LEVEL_4_2V           4096  // 4.2V对应的ADC值 (2.1V分压后)
#define BATT_ADC_LEVEL_2_5V           2381  // 2.5V对应的ADC值 (1.25V分压后)

// 锂电池电压-容量对应表 (分压后的电压值)
typedef struct {
    uint16_t adc_value;    // ADC值
    uint8_t percentage;    // 对应的电量百分比
} batt_voltage_table_t;

// 根据您提供的表格，转换为分压后的ADC值
// 注意：查找表必须按ADC值从大到小排序，因为查找逻辑是从大到小查找
// 新的电池电量映射表 (基于您提供的电压值)
// 电压值 -> 分压后电压 -> ADC值 (Vref=1.05V)
// 注意：查找表必须按ADC值从大到小排序
static const batt_voltage_table_t batt_voltage_table[] = {
    {4096, 100},  // 4200mV -> 2100mV -> ADC 4096 (saturated)
    {4096, 100},  // 4100mV -> 2050mV -> ADC 4096 (saturated)
    {3986, 90},   // 4060mV -> 2030mV -> ADC 3986
    {3906, 80},   // 3980mV -> 1990mV -> ADC 3906
    {3847, 70},   // 3920mV -> 1960mV -> ADC 3847
    {3798, 60},   // 3870mV -> 1935mV -> ADC 3798
    {3748, 50},   // 3820mV -> 1910mV -> ADC 3748
    {3723, 40},   // 3790mV -> 1895mV -> ADC 3723
    {3698, 30},   // 3770mV -> 1885mV -> ADC 3698
    {3673, 20},   // 3740mV -> 1870mV -> ADC 3673
    {3611, 10},   // 3680mV -> 1840mV -> ADC 3611
    {3386, 5},    // 3450mV -> 1725mV -> ADC 3386
    {2943, 0}     // 3000mV -> 1500mV -> ADC 2943
};

#define BATT_TABLE_SIZE (sizeof(batt_voltage_table) / sizeof(batt_voltage_table[0]))

//#define NULL                        0
#define FALSE                        0
#define BATT_LEVEL_VALUE_IDX        2 // Position of battery level in attribute array
#define BATT_LEVEL_VALUE_CCCD_IDX   3 // Position of battery level CCCD in attribute array

#define BATT_LEVEL_VALUE_LEN        1


//#define BATT_ADC_LEVEL_3V           222
//#define BATT_ADC_LEVEL_2V           111
//#define NULL                        0
//#define FALSE                        0
//#define BATT_LEVEL_VALUE_IDX        2// Position of battery level in attribute array
//#define BATT_LEVEL_VALUE_CCCD_IDX   3 // Position of battery level CCCD in attribute array
//
//#define BATT_LEVEL_VALUE_LEN       1
/*********************************************************************
 * TYPEDEFS
 */

/*********************************************************************
 * GLOBAL VARIABLES
 */
// Battery service
const uint8 battServUUID[ATT_BT_UUID_SIZE] =
{
  LO_UINT16(BATT_SERV_UUID), HI_UINT16(BATT_SERV_UUID)
};

// Battery level characteristic
const uint8 battLevelUUID[ATT_BT_UUID_SIZE] =
{
  LO_UINT16(BATT_LEVEL_UUID), HI_UINT16(BATT_LEVEL_UUID)
};

// Characteristic Presentation Format UUID (0x2904)
const uint8 presentationFormatUUID[ATT_BT_UUID_SIZE] =
{
  LO_UINT16(0x2904), HI_UINT16(0x2904)
};

/*********************************************************************
 * EXTERNAL VARIABLES
 */

/*********************************************************************
 * EXTERNAL FUNCTIONS
 */

/*********************************************************************
 * LOCAL VARIABLES
 */

// Application callback
static battServiceCB_t battServiceCB;

// Measurement setup callback
static battServiceSetupCB_t battServiceSetupCB = NULL;

// Measurement teardown callback
static battServiceTeardownCB_t battServiceTeardownCB = NULL;

// Measurement calculation callback
static battServiceCalcCB_t battServiceCalcCB = NULL;

static uint16 battMinLevel = BATT_ADC_LEVEL_2_5V; // 最小电压对应的ADC值
static uint16 battMaxLevel = BATT_ADC_LEVEL_4_2V; // 最大电压对应的ADC值

// Critical battery level setting
static uint8 battCriticalLevel;

/*********************************************************************
 * Profile Attributes - variables
 */

// Battery Service attribute
static const gattAttrType_t battService = { ATT_BT_UUID_SIZE, battServUUID };

// Battery level characteristic
static uint8 battLevelProps = GATT_PROP_READ | GATT_PROP_NOTIFY;
static uint8 battLevel = 255;  // 设置为无效值，确保首次测量时触发通知
static gattCharCfg_t battLevelClientCharCfg[GATT_MAX_NUM_CONN];

// HID Report Reference characteristic descriptor, battery level
static uint8 hidReportRefBattLevel[HID_REPORT_REF_LEN] =
             { HID_RPT_ID_BATT_LEVEL_IN, HID_REPORT_TYPE_INPUT };

// Battery Level Presentation Format Descriptor - 指明电量单位为百分比(0-100)
static uint8 battLevelFormat[7] = {
    0x04,       // Format: uint8 (1 byte unsigned integer)
    0x00,       // Exponent: 0 (no scaling)
    0xAD, 0x27, // Unit: percentage (0x27AD)
    0x01,       // Namespace: Bluetooth SIG
    0x00, 0x00  // Description: None
};

/*********************************************************************
 * Profile Attributes - Table
 */

static gattAttribute_t battAttrTbl[] =
{
  // Battery Service
  {
    { ATT_BT_UUID_SIZE, primaryServiceUUID }, /* type */
    GATT_PERMIT_READ,                         /* permissions */
    0,                                        /* handle */
    (uint8 *)&battService                     /* pValue */
  },

    // Battery Level Declaration
    {
      { ATT_BT_UUID_SIZE, characterUUID },
      GATT_PERMIT_READ,
      0,
      &battLevelProps
    },

      // Battery Level Value
      {
        { ATT_BT_UUID_SIZE, battLevelUUID },
        GATT_PERMIT_READ,
        0,
        &battLevel
      },

      // Battery Level Client Characteristic Configuration
      {
        { ATT_BT_UUID_SIZE, clientCharCfgUUID },
        GATT_PERMIT_READ | GATT_PERMIT_WRITE, // 允许未加密状态下写入，解决Windows设备配对前的CCCD问题
        0,
        (uint8 *) &battLevelClientCharCfg
      },

      // HID Report Reference characteristic descriptor, batter level input
      {
        { ATT_BT_UUID_SIZE, reportRefUUID },
        GATT_PERMIT_READ,
        0,
        hidReportRefBattLevel
      },

      // Battery Level Presentation Format Descriptor
      {
        { ATT_BT_UUID_SIZE, presentationFormatUUID }, // Characteristic Presentation Format UUID (0x2904)
        GATT_PERMIT_READ,
        0,
        battLevelFormat
      }
};


/*********************************************************************
 * LOCAL FUNCTIONS
 */
static bStatus_t battReadAttrCB( uint16 connHandle, gattAttribute_t *pAttr,
                             uint8 *pValue, uint16 *pLen, uint16 offset, uint16 maxLen,uint8 method );
static bStatus_t battWriteAttrCB( uint16 connHandle, gattAttribute_t *pAttr,
                                  uint8 *pValue, uint16 len, uint16 offset,uint8 method );
static void battNotifyCB( linkDBItem_t *pLinkItem );
static uint8 battMeasure( void );
static void battNotifyLevel( void );

/*********************************************************************
 * PROFILE CALLBACKS
 */
// Battery Service Callbacks
gattServiceCBs_t battCBs =
{
  battReadAttrCB,  // Read callback function pointer
  battWriteAttrCB, // Write callback function pointer
  NULL             // Authorization callback function pointer
};

/*********************************************************************
 * PUBLIC FUNCTIONS
 */

/*********************************************************************
 * @fn      Batt_AddService
 *
 * @brief   Initializes the Battery Service by registering
 *          GATT attributes with the GATT server.
 *
 * @return  Success or Failure
 */
bStatus_t Batt_AddService( void )
{
  uint8 status = SUCCESS;

  PRINT("=== Initializing Battery Service ===\n");

  // Initialize Client Characteristic Configuration attributes
  GATTServApp_InitCharCfg( INVALID_CONNHANDLE, battLevelClientCharCfg );

  // Register GATT attribute list and CBs with GATT Server App
  status = GATTServApp_RegisterService( battAttrTbl,
                                        GATT_NUM_ATTRS( battAttrTbl ),
										GATT_MAX_ENCRYPT_KEY_SIZE,
                                        &battCBs );

  if (status == SUCCESS) {
      PRINT("✅ Battery Service registered successfully\n");
      PRINT("   Service UUID: 0x180F (Battery Service)\n");
      PRINT("   Characteristic UUID: 0x2A19 (Battery Level)\n");
      PRINT("   Attributes count: %d\n", GATT_NUM_ATTRS( battAttrTbl ));
  } else {
      PRINT("❌ Failed to register Battery Service, status: 0x%02X\n", status);
  }

  PRINT("=== Battery Service Initialization Complete ===\n");

  return ( status );
}

/*********************************************************************
 * @fn      Batt_Register
 *
 * @brief   Register a callback function with the Battery Service.
 *
 * @param   pfnServiceCB - Callback function.
 *
 * @return  None.
 */
extern void Batt_Register( battServiceCB_t pfnServiceCB )
{
  PRINT("Registering Battery Service callback...\n");
  battServiceCB = pfnServiceCB;
  PRINT("Battery Service callback registered successfully\n");
}

/*********************************************************************
 * @fn      Batt_SetParameter
 *
 * @brief   Set a Battery Service parameter.
 *
 * @param   param - Profile parameter ID
 * @param   len - length of data to right
 * @param   value - pointer to data to write.  This is dependent on
 *          the parameter ID and WILL be cast to the appropriate
 *          data type (example: data type of uint16 will be cast to
 *          uint16 pointer).
 *
 * @return  bStatus_t
 */
bStatus_t Batt_SetParameter( uint8 param, uint8 len, void *value )
{
  bStatus_t ret = SUCCESS;
//  PRINT("Batt_SetParameter\n");
  switch ( param )
  {
    case BATT_PARAM_CRITICAL_LEVEL:
      battCriticalLevel = *((uint8*)value);

      // If below the critical level and critical state not set, notify it
      if ( battLevel < battCriticalLevel)
      {
        battNotifyLevel();
      }
      break;

    default:
      ret = INVALIDPARAMETER;
      break;
  }

  return ( ret );
}

/*********************************************************************
 * @fn      Batt_GetParameter
 *
 * @brief   Get a Battery Service parameter.
 *
 * @param   param - Profile parameter ID
 * @param   value - pointer to data to get.  This is dependent on
 *          the parameter ID and WILL be cast to the appropriate
 *          data type (example: data type of uint16 will be cast to
 *          uint16 pointer).
 *
 * @return  bStatus_t
 */
bStatus_t Batt_GetParameter( uint8 param, void *value )
{
  bStatus_t ret = SUCCESS;
//  PRINT("Batt_GetParameter\n");
  switch ( param )
  {
    case BATT_PARAM_LEVEL:
      *((uint8*)value) = battLevel;
      break;

    case BATT_PARAM_CRITICAL_LEVEL:
      *((uint8*)value) = battCriticalLevel;
      break;

    case BATT_PARAM_SERVICE_HANDLE:
      *((uint16*)value) = GATT_SERVICE_HANDLE( battAttrTbl );
      break;

    case BATT_PARAM_BATT_LEVEL_IN_REPORT:
      {
        hidRptMap_t *pRpt = (hidRptMap_t *)value;

        pRpt->id = hidReportRefBattLevel[0];
        pRpt->type = hidReportRefBattLevel[1];
        pRpt->handle = battAttrTbl[BATT_LEVEL_VALUE_IDX].handle;
        pRpt->cccdHandle = battAttrTbl[BATT_LEVEL_VALUE_CCCD_IDX].handle;
        pRpt->mode = HID_PROTOCOL_MODE_REPORT;
      }
      break;

    default:
      ret = INVALIDPARAMETER;
      break;
  }

  return ( ret );
}

/*********************************************************************
 * @fn          Batt_MeasLevel
 *
 * @brief       Measure the battery level and update the battery
 *              level value in the service characteristics.  If
 *              the battery level-state characteristic is configured
 *              for notification and the battery level has changed
 *              since the last measurement, then a notification
 *              will be sent.
 *
 * @return      Success
 */
bStatus_t Batt_MeasLevel( void )
{
  // 重定向到统一的电池管理函数
  PRINT("Batt_MeasLevel called - redirecting to unified battery management\n");
  MeasureAndReportBatteryLevel();
  return SUCCESS;
}

/*********************************************************************
 * @fn      Batt_ForceNotify
 *
 * @brief   强制发送电池电量通知，用于测试
 *
 * @return  None
 */
void Batt_ForceNotify( void )
{
  PRINT("Batt_ForceNotify called - forcing battery notification\n");
  battNotifyLevel();
}

/*********************************************************************
 * UNIFIED BATTERY MANAGEMENT MODULE
 * 统一的电池管理模块 - 高度封装的电池电量检测与上报
 *********************************************************************/

// 基础物理常量配置
#define VREF_MV                   1050   // ADC参考电压 1.05V
#define ADC_FULL_SCALE_MV         VREF_MV
#define ADC_RESOLUTION            4096   // 12位ADC
#define VOLTAGE_DIVIDER_RATIO     2.0f   // 1:1分压电阻，实际电池电压是测量值的2倍

// ADC采样配置
#define ADC_SAMPLES_FOR_AVERAGE   20     // 每次测量采样的次数

// 全局状态变量
static uint8_t g_last_reported_battery_level = 0xFF; // 上次上报的电量值(初值为无效值)
static uint32_t g_measurement_count = 0;             // 测量次数计数

/*********************************************************************
 * @fn      read_adc_raw_value
 *
 * @brief   硬件相关的ADC读取函数
 *
 * @return  ADC原始值
 */
static uint16_t read_adc_raw_value(void)
{
    // 配置ADC
    ADC_ExtSingleChSampInit( SampleFreq_3_2, ADC_PGA_0 );
    ADC_ChannelCfg( 3 );

    // 配置PA13为浮空输入
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_Floating);
    DelayMs(5);  // 等待稳定

    // 执行ADC转换
    uint16_t adc_value = ADC_ExcutSingleConver();

    // 恢复PA13为上拉模式
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_PU);

    return adc_value;
}

/*********************************************************************
 * @fn      validate_battery_level
 *
 * @brief   验证电池电量值在有效范围内(0-100)
 *
 * @param   level - 电池电量值
 *
 * @return  验证后的电池电量值
 */
static uint8_t validate_battery_level(uint8_t level)
{
    if (level > 100) {
        PRINT("⚠️  Battery level %d%% exceeds maximum, clamping to 100%%\n", level);
        return 100;
    }
    // 0% 是有效的，不需要修正
    return level;
}

/*********************************************************************
 * @fn      adc_to_battery_percent
 *
 * @brief   将ADC原始值精确转换为电池电量百分比
 *
 * @param   adc_raw_value - ADC原始值
 *
 * @return  电池电量百分比 (0-100)
 */
static uint8_t adc_to_battery_percent(uint16_t adc_raw_value)
{
    // 计算引脚电压 (mV) - 使用1.05V参考电压
    uint32_t pin_voltage_mv = ((uint32_t)adc_raw_value * ADC_FULL_SCALE_MV) / ADC_RESOLUTION;

    // 计算实际电池电压 (考虑分压比)
    uint16_t battery_voltage_mv = (uint16_t)(pin_voltage_mv * VOLTAGE_DIVIDER_RATIO);

    PRINT("ADC=%d, Pin_V=%lumV (Vref=1.05V), Batt_V=%dmV", adc_raw_value, pin_voltage_mv, battery_voltage_mv);

    // 使用现有的查找表进行电量计算
    uint8_t percent = 0;
    for (uint16_t i = 0; i < BATT_TABLE_SIZE; i++) {
        if (adc_raw_value >= batt_voltage_table[i].adc_value) {
            percent = batt_voltage_table[i].percentage;
            PRINT(" -> %d%% (table[%d])\n", percent, i);
            break;
        }
    }

    // 确保电量在0-100%之间
    percent = validate_battery_level(percent);

    return percent;
}

/*********************************************************************
 * @fn      check_ble_notification_ready
 *
 * @brief   检查BLE连接状态和通知使能状态
 *
 * @return  TRUE - 可以发送通知, FALSE - 不能发送通知
 */
static uint8_t check_ble_notification_ready(void)
{
    // 检查BLE连接状态
    extern uint16 hidEmuConnHandle;  // 来自hidkbd.c的连接句柄

    if (hidEmuConnHandle == GAP_CONNHANDLE_INIT) {
        PRINT("❌ BLE not connected (handle: 0x%04X)\n", hidEmuConnHandle);
        return FALSE;
    }

    PRINT("✅ BLE connected (handle: 0x%04X)\n", hidEmuConnHandle);
    
    // 检查客户端特征配置描述符(CCCD)状态
    uint16 cccd_value = GATTServApp_ReadCharCfg(hidEmuConnHandle, battLevelClientCharCfg);
    PRINT("CCCD value: 0x%04X\n", cccd_value);
    
    if (cccd_value & GATT_CLIENT_CFG_NOTIFY) {
        PRINT("✅ Client notifications enabled - ready to send\n");
        return TRUE;
    } else {
        PRINT("⚠️  Client notifications NOT enabled (CCCD=0x%04X)\n", cccd_value);
        PRINT("   正常情况 - 客户端需要主动启用通知\n");
        PRINT("   建议使用 Batt_ForceEnableNotifications() 强制启用\n");
        return FALSE; // 严格按照BLE协议，不在CCCD未启用时发送
    }
}

/*********************************************************************
 * @fn      MeasureAndReportBatteryLevel
 *
 * @brief   测量并上报电池电量 (如果需要)
 *          这是一个高级封装函数，完成了从测量到蓝牙上报的所有逻辑。
 *          应由主任务或定时器周期性调用。
 *
 * @return  None
 */
void MeasureAndReportBatteryLevel(void)
{
    g_measurement_count++;

    PRINT("\n=== UNIFIED BATTERY MEASUREMENT #%lu ===\n", g_measurement_count);

    // --- 步骤 1: 进行多次ADC采样并求平均，以获得稳定值 ---
    PRINT("Step 1: Performing %d ADC samples for averaging...\n", ADC_SAMPLES_FOR_AVERAGE);

    // 配置ADC和GPIO
    ADC_ExtSingleChSampInit( SampleFreq_3_2, ADC_PGA_0 );
    ADC_ChannelCfg( 3 );
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_Floating);

    // 等待电路稳定
    PRINT("Waiting for circuit stabilization (200ms)...\n");
    DelayMs(200);

    // 丢弃第一次采样
    uint16_t discard = ADC_ExcutSingleConver();
    PRINT("Discarded first sample: %d\n", discard);
    DelayMs(10);

    // 进行多次采样求平均
    uint32_t adc_sum = 0;
    for (int i = 0; i < ADC_SAMPLES_FOR_AVERAGE; i++) {
        uint16_t sample = ADC_ExcutSingleConver();
        adc_sum += sample;
        if (i < 3) {  // 显示前3个样本
            PRINT("Sample %d: %d\n", i+1, sample);
        }
        DelayMs(1);
    }

    // 恢复GPIO状态
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_PU);

    uint16_t avg_adc_value = (uint16_t)(adc_sum / ADC_SAMPLES_FOR_AVERAGE);
    PRINT("ADC average: %d (sum=%lu)\n", avg_adc_value, adc_sum);

    // --- 步骤 2: 计算当前电量百分比 ---
    PRINT("Step 2: Converting ADC to battery percentage...\n");
    uint8_t current_level = adc_to_battery_percent(avg_adc_value);
    PRINT("Calculated battery level: %d%%\n", current_level);

    // --- 步骤 3: 检查BLE连接状态 ---
    PRINT("Step 3: Checking BLE connection status...\n");
    if (!check_ble_notification_ready()) {
        PRINT("⚠️  BLE not connected - skipping battery notification\n");
        PRINT("   Battery level measured: %d%% (not reported)\n", current_level);
        PRINT("=== UNIFIED BATTERY MEASUREMENT COMPLETED ===\n\n");
        return;
    }

    // --- 步骤 4: 检查电量是否变化，以及是否可以发送通知 ---
    PRINT("Step 4: Checking if notification is needed...\n");
    PRINT("Current: %d%%, Last reported: %d%%\n", current_level, g_last_reported_battery_level);

    uint8_t should_notify = 0;

    if (g_last_reported_battery_level == 0xFF) {
        // 首次测量
        should_notify = 1;
        PRINT("Reason: First measurement\n");
    } else if (current_level != g_last_reported_battery_level) {
        // 电量发生变化
        should_notify = 1;
        PRINT("Reason: Battery level changed\n");
    } else if (g_measurement_count % 5 == 0) {
        // 每5次强制通知
        should_notify = 1;
        PRINT("Reason: Periodic forced notification (every 5 measurements)\n");
    } else {
        PRINT("Reason: No notification needed\n");
    }

    if (should_notify) {
        PRINT("Step 5: Sending battery notification...\n");

        // 更新全局电量值
        battLevel = current_level;

        // 发送通知（即使客户端未启用通知也尝试发送）
        PRINT("Attempting to send notification (will check client config in callback)...\n");
        battNotifyLevel();

        // 更新记录
        g_last_reported_battery_level = current_level;
        PRINT("✅ Battery level updated: %d%%\n", current_level);
    } else {
        PRINT("⏭️  Skipping notification (no change)\n");
    }

    PRINT("=== UNIFIED BATTERY MEASUREMENT COMPLETED ===\n\n");
}

/*********************************************************************
 * @fn      Batt_Setup
 *
 * @brief   Set up which ADC source is to be used. Defaults to VDD/3.
 *
 * @param   adc_ch - ADC Channel, e.g. HAL_ADC_CHN_AIN6
 * @param   minVal - max battery level
 * @param   maxVal - min battery level
 * @param   sCB - HW setup callback
 * @param   tCB - HW tear down callback
 * @param   cCB - percentage calculation callback
 *
 * @return  none.
 */
void Batt_Setup( uint8 adc_ch, uint16 minVal, uint16 maxVal,
                 battServiceSetupCB_t sCB, battServiceTeardownCB_t tCB,
                 battServiceCalcCB_t cCB )
{
  //battServiceAdcCh = adc_ch;
  battMinLevel = minVal;
  battMaxLevel = maxVal;

  battServiceSetupCB = sCB;
  battServiceTeardownCB = tCB;
  battServiceCalcCB = cCB;
}

/*********************************************************************
 * @fn          battReadAttrCB
 *
 * @brief       Read an attribute.
 *
 * @param       connHandle - connection message was received on
 * @param       pAttr - pointer to attribute
 * @param       pValue - pointer to data to be read
 * @param       pLen - length of data to be read
 * @param       offset - offset of the first octet to be read
 * @param       maxLen - maximum length of data to be read
 *
 * @return      Success or Failure
 */
static bStatus_t battReadAttrCB( uint16 connHandle, gattAttribute_t *pAttr,
                             uint8 *pValue, uint16 *pLen, uint16 offset, uint16 maxLen,uint8 method )
{
  uint16 uuid;
  bStatus_t status = SUCCESS;
//  PRINT("battReadAttrCB\n");
  // Make sure it's not a blob operation (no attributes in the profile are long)
  if ( offset > 0 )
  {
    return ( ATT_ERR_ATTR_NOT_LONG );
  }

  uuid = BUILD_UINT16( pAttr->type.uuid[0], pAttr->type.uuid[1] );

  // Measure battery level if reading level
  if ( uuid == BATT_LEVEL_UUID )
  {
    uint8 level;

    level = battMeasure();

    // 移除电量下降的限制，允许电量上升和下降
    // 每次读取都更新电量值，确保实时性
    battLevel = level;

    *pLen = 1;
    pValue[0] = battLevel;
  }
  else if ( uuid == GATT_REPORT_REF_UUID )
  {
    *pLen = HID_REPORT_REF_LEN;
    tmos_memcpy( pValue, pAttr->pValue, HID_REPORT_REF_LEN );
  }
  else
  {
    status = ATT_ERR_ATTR_NOT_FOUND;
  }

  return ( status );
}

/*********************************************************************
 * @fn      battWriteAttrCB
 *
 * @brief   Validate attribute data prior to a write operation
 *
 * @param   connHandle - connection message was received on
 * @param   pAttr - pointer to attribute
 * @param   pValue - pointer to data to be written
 * @param   len - length of data
 * @param   offset - offset of the first octet to be written
 *
 * @return  Success or Failure
 */
static bStatus_t battWriteAttrCB( uint16 connHandle, gattAttribute_t *pAttr,
                                  uint8 *pValue, uint16 len, uint16 offset,uint8 method )
{
  bStatus_t status = SUCCESS;
  PRINT("=== battWriteAttrCB called ===\n");
  PRINT("Connection handle: 0x%04X\n", connHandle);
  PRINT("Data length: %d\n", len);
  PRINT("Offset: %d\n", offset);
  
  uint16 uuid = BUILD_UINT16( pAttr->type.uuid[0], pAttr->type.uuid[1]);
  PRINT("Attribute UUID: 0x%04X\n", uuid);
  
  switch ( uuid )
  {
    case GATT_CLIENT_CHAR_CFG_UUID:
      PRINT("✅ CCCD write request detected\n");
      
      if (len >= 2) {
          uint16 new_cccd_value = BUILD_UINT16( pValue[0], pValue[1] );
          PRINT("New CCCD value: 0x%04X\n", new_cccd_value);
          
          if (new_cccd_value & GATT_CLIENT_CFG_NOTIFY) {
              PRINT("✅ Client is ENABLING notifications\n");
          } else {
              PRINT("⚠️  Client is DISABLING notifications\n");
          }
      }
      
      status = GATTServApp_ProcessCCCWriteReq( connHandle, pAttr, pValue, len,
                                               offset, GATT_CLIENT_CFG_NOTIFY );
      
      PRINT("CCCD write processing result: 0x%02X\n", status);
      
      if ( status == SUCCESS )
      {
        uint16 charCfg = BUILD_UINT16( pValue[0], pValue[1] );
        PRINT("Final CCCD configuration: 0x%04X\n", charCfg);

        if ( battServiceCB )
        {
          uint8_t callback_param = (charCfg == GATT_CFG_NO_OPERATION) ?
                            BATT_LEVEL_NOTI_DISABLED :
                            BATT_LEVEL_NOTI_ENABLED;
          
          PRINT("Calling battery service callback with parameter: %d\n", callback_param);
          (*battServiceCB)(callback_param);
        }
        
        // 如果通知被启用，立即发送一次电池电量通知
        if (charCfg & GATT_CLIENT_CFG_NOTIFY) {
            PRINT("✅ Notifications enabled - sending immediate battery update\n");
            // 立即进行一次电池测量和通知
            extern void MeasureAndReportBatteryLevel(void);
            MeasureAndReportBatteryLevel();
        }
      }
      break;

    default:
      PRINT("⚠️  Unknown attribute UUID\n");
      status = ATT_ERR_ATTR_NOT_FOUND;
      break;
  }
  
  PRINT("=== battWriteAttrCB completed with status: 0x%02X ===\n\n", status);
  return ( status );
}

/*********************************************************************
 * @fn          battNotifyCB
 *
 * @brief       Send a notification of the level state characteristic.
 *
 * @param       connHandle - linkDB item
 *
 * @return      None.
 */
static void battNotifyCB( linkDBItem_t *pLinkItem )
{
  PRINT("=== battNotifyCB DEBUG INFO ===\n");
  PRINT("Connection handle: 0x%04X\n", pLinkItem->connectionHandle);
  PRINT("State flags: 0x%02X\n", pLinkItem->stateFlags);
  PRINT("Battery level to send: %d%%\n", battLevel);

  if ( pLinkItem->stateFlags & LINK_CONNECTED )
  {
    PRINT("✅ Connection is ACTIVE\n");
    
    uint16 cccd_value = GATTServApp_ReadCharCfg( pLinkItem->connectionHandle,
                                            battLevelClientCharCfg );
    PRINT("CCCD configuration: 0x%04X\n", cccd_value);
    PRINT("Expected NOTIFY bit: 0x%04X\n", GATT_CLIENT_CFG_NOTIFY);
    
    // 无论CCCD状态如何，都尝试发送通知（用于诊断）
    if ( cccd_value & GATT_CLIENT_CFG_NOTIFY )
    {
      PRINT("✅ Client has ENABLED notifications\n");
    } else {
      PRINT("⚠️  Client has NOT enabled notifications\n");
      PRINT("   按照BLE协议要求，不发送未启用的通知\n");
      PRINT("   建议使用 Batt_ForceEnableNotifications() 强制启用\n");
      PRINT("=== battNotifyCB COMPLETED (SKIPPED) ===\n\n");
      return; // 严格遵循BLE协议，不发送未启用的通知
    }
    
    // 尝试发送通知
    PRINT("Allocating notification memory...\n");
    attHandleValueNoti_t noti;
    noti.pValue = GATT_bm_alloc(pLinkItem->connectionHandle, ATT_HANDLE_VALUE_NOTI,
                              BATT_LEVEL_VALUE_LEN, NULL, 0);
    
    if (noti.pValue != NULL)
    {
      PRINT("✅ Memory allocated successfully\n");
      noti.handle = battAttrTbl[BATT_LEVEL_VALUE_IDX].handle;
      noti.len = BATT_LEVEL_VALUE_LEN;
      noti.pValue[0] = battLevel;
      
      PRINT("Notification details:\n");
      PRINT("  Handle: 0x%04X\n", noti.handle);
      PRINT("  Length: %d\n", noti.len);
      PRINT("  Value: %d\n", noti.pValue[0]);
      
      bStatus_t status = GATT_Notification(pLinkItem->connectionHandle, &noti, FALSE);
      
      if (status == SUCCESS) {
          PRINT("✅ NOTIFICATION SENT SUCCESSFULLY!\n");
          if (!(cccd_value & GATT_CLIENT_CFG_NOTIFY)) {
              PRINT("   Note: Sent despite CCCD not enabled\n");
          }
      } else {
          PRINT("❌ NOTIFICATION FAILED with status: 0x%02X\n", status);
          switch(status) {
              case bleNotConnected:
                  PRINT("   Reason: Not connected\n");
                  break;
              case bleMemAllocError:
                  PRINT("   Reason: Memory allocation error\n");
                  break;
              case INVALIDPARAMETER:
                  PRINT("   Reason: Invalid parameter\n");
                  break;
              default:
                  PRINT("   Reason: Unknown error code\n");
                  break;
          }
          GATT_bm_free((gattMsg_t *)&noti, ATT_HANDLE_VALUE_NOTI);
      }
    } else {
        PRINT("❌ FAILED to allocate notification memory\n");
    }
  } else {
      PRINT("❌ Connection NOT ACTIVE (stateFlags: 0x%02X)\n", pLinkItem->stateFlags);
      PRINT("   Expected LINK_CONNECTED flag: 0x%02X\n", LINK_CONNECTED);
  }
  
  PRINT("=== battNotifyCB COMPLETED ===\n\n");
}

/*********************************************************************
 * @fn      battMeasure
 *
 * @brief   Measure the battery level with the ADC and return
 *          it as a percentage 0-100%.
 *
 * @return  Battery level.
 */
// 旧的battMeasure函数已删除，统一使用MeasureAndReportBatteryLevel()
static uint8 battMeasure( void )
{
  // 重定向到统一的电池管理函数进行测量
  // 这里只返回当前的battLevel值，实际测量由MeasureAndReportBatteryLevel()完成
  PRINT("battMeasure called - returning current battery level: %d%%\n", battLevel);
  return battLevel;
}

/*********************************************************************
 * @fn      battNotifyLevelState
 *
 * @brief   Send a notification of the battery level state
 *          characteristic if a connection is established.
 *
 * @return  None.
 */
static void battNotifyLevel( void )
{
  // Execute linkDB callback to send notification
  PRINT("battNotifyLevel called - checking all connections...\n");
  linkDB_PerformFunc( battNotifyCB );
  PRINT("battNotifyLevel completed\n");
}

/*********************************************************************
 * @fn          Batt_HandleConnStatusCB
 *
 * @brief       Battery Service link status change handler function.
 *
 * @param       connHandle - connection handle
 * @param       changeType - type of change
 *
 * @return      none
 */
void Batt_HandleConnStatusCB( uint16 connHandle, uint8 changeType )
{
  PRINT("=== Batt_HandleConnStatusCB ===\n");
  PRINT("Connection Handle: 0x%04X\n", connHandle);
  PRINT("Change Type: 0x%02X\n", changeType);
  
  // Make sure this is not loopback connection
  if ( connHandle != LOOPBACK_CONNHANDLE )
  {
    // Reset Client Char Config if connection has dropped
    if ( ( changeType == LINKDB_STATUS_UPDATE_REMOVED )      ||
         ( ( changeType == LINKDB_STATUS_UPDATE_STATEFLAGS ) &&
           ( !linkDB_Up( connHandle ) ) ) )
    {
      PRINT("⚠️  Connection dropped - resetting CCCD for handle: 0x%04X\n", connHandle);
      
      // 重置该连接的CCCD状态为默认值（通知禁用）
      GATTServApp_InitCharCfg( connHandle, battLevelClientCharCfg );
      
      PRINT("✅ CCCD reset completed for disconnected client\n");
    }
    else if ( changeType == LINKDB_STATUS_UPDATE_NEW )
    {
      PRINT("✅ New connection established - initializing CCCD for handle: 0x%04X\n", connHandle);
      
      // 新连接建立，初始化CCCD状态
      GATTServApp_InitCharCfg( connHandle, battLevelClientCharCfg );
    }
  }
  
  PRINT("=== Batt_HandleConnStatusCB Completed ===\n\n");
}

/*********************************************************************
 * @fn      Batt_ForceEnableNotifications
 *
 * @brief   强制启用电池通知功能，用于解决Windows客户端不自动启用通知的问题
 *
 * @return  None
 */
void Batt_ForceEnableNotifications( void )
{
    PRINT("\n=== FORCE ENABLE BATTERY NOTIFICATIONS ===\n");
    
    // 获取当前连接句柄
    extern uint16 hidEmuConnHandle;
    
    if (hidEmuConnHandle == GAP_CONNHANDLE_INIT) {
        PRINT("❌ No BLE connection available\n");
        PRINT("=== FORCE ENABLE FAILED ===\n\n");
        return;
    }
    
    PRINT("✅ BLE connection found (handle: 0x%04X)\n", hidEmuConnHandle);
    
    // 检查连接是否正常
    if (!linkDB_Up(hidEmuConnHandle)) {
        PRINT("❌ Connection is not active\n");
        PRINT("=== FORCE ENABLE FAILED ===\n\n");
        return;
    }
    
    // 检查当前CCCD状态
    uint16 current_cccd = GATTServApp_ReadCharCfg(hidEmuConnHandle, battLevelClientCharCfg);
    PRINT("Current CCCD value: 0x%04X\n", current_cccd);
    
    if (current_cccd & GATT_CLIENT_CFG_NOTIFY) {
        PRINT("✅ Notifications already enabled\n");
        PRINT("Sending immediate battery notification for verification...\n");
        battNotifyLevel();
    } else {
        PRINT("⚠️  Notifications not enabled, forcing enable...\n");
        
        // 直接设置CCCD值为启用通知
        uint16 new_cccd_value = GATT_CLIENT_CFG_NOTIFY;
        bStatus_t status = GATTServApp_WriteCharCfg(hidEmuConnHandle, 
                                                   battLevelClientCharCfg,
                                                   new_cccd_value);
        
        if (status == SUCCESS) {
            PRINT("✅ CCCD successfully set to 0x%04X\n", new_cccd_value);
            
            // 等待一小段时间，让设置生效
            DelayMs(100);
            
            // 验证设置是否成功
            uint16 verify_cccd = GATTServApp_ReadCharCfg(hidEmuConnHandle, battLevelClientCharCfg);
            PRINT("Verification CCCD value: 0x%04X\n", verify_cccd);
            
            if (verify_cccd & GATT_CLIENT_CFG_NOTIFY) {
                PRINT("✅ Notification enable verification PASSED\n");
                
                // 触发回调通知应用层
                if (battServiceCB) {
                    PRINT("Calling battery service callback...\n");
                    (*battServiceCB)(BATT_LEVEL_NOTI_ENABLED);
                }
                
                // 立即发送一次电池电量通知进行测试
                PRINT("Sending test battery notification...\n");
                battNotifyLevel();
                
            } else {
                PRINT("❌ Notification enable verification FAILED\n");
                PRINT("   Possible reasons:\n");
                PRINT("   1. Connection dropped during operation\n");
                PRINT("   2. GATT write was rejected by stack\n");
                PRINT("   3. Client rejected the configuration\n");
            }
        } else {
            PRINT("❌ Failed to set CCCD, status: 0x%02X\n", status);
            PRINT("   Possible reasons:\n");
            PRINT("   1. Invalid connection handle\n");
            PRINT("   2. GATT operation already in progress\n");
            PRINT("   3. Insufficient memory or resources\n");
        }
    }
    
    PRINT("=== FORCE ENABLE COMPLETED ===\n\n");
}

/*********************************************************************
 * @fn      Batt_GetCCCDStatus
 *
 * @brief   获取当前CCCD状态，用于调试
 *
 * @return  CCCD值
 */
uint16_t Batt_GetCCCDStatus( void )
{
    extern uint16 hidEmuConnHandle;
    
    if (hidEmuConnHandle == GAP_CONNHANDLE_INIT) {
        PRINT("No BLE connection for CCCD check\n");
        return 0x0000;
    }
    
    uint16 cccd_value = GATTServApp_ReadCharCfg(hidEmuConnHandle, battLevelClientCharCfg);
    PRINT("Current CCCD status: 0x%04X (notifications %s)\n", 
          cccd_value, 
          (cccd_value & GATT_CLIENT_CFG_NOTIFY) ? "ENABLED" : "DISABLED");
    
    return cccd_value;
}

/*********************************************************************
*********************************************************************/
