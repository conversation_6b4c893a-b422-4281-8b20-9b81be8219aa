/********************************** (C) COPYRIGHT *******************************
 * File Name          : main.c
 * Author             : WCH
 * Version            : V1.1
 * Date               : 2020/08/06
 * Description        : ����ӻ�Ӧ��������������ϵͳ��ʼ��
 *******************************************************************************/

/******************************************************************************/
/* ͷ�ļ����� */
#include "CH58x_common.h"
#include "HAL/config.h"
#include "HAL_FLASH/include/easyflash.h"
#include "device_config.h"
#include "RingBuffer/lwrb.h"
#include "config.h"
#include "RF_PHY/rf_sync.h"
#include "I2C/myi2c.h"
#include "power_led/sleep_manager.h"

// 添加电池服务相关的类型定义
typedef uint8_t bStatus_t;

bool isUSBinsert = false;
/*********************************************************************
 * GLOBAL TYPEDEFS
 */
__attribute__((aligned(4)))   u32 MEM_BUF[BLE_MEMHEAP_SIZE / 4];

uint8_t MacAddr[6] = { 0x84, 0xC2, 0xE4, 0x13, 0x33, 0x44 };

/*******************************************************************************
 * Function Name  : Main_Circulation
 * Description    : ��ѭ��
 * Input          : None
 * Output         : None
 * Return         : None
 *******************************************************************************/
__attribute__((section(".highcode")))
void Main_Circulation() {
    while (1) {
//        rf_sync();
        TMOS_SystemProcess();
    }
}

/*******************************************************************************
 * Function Name  : main
 * Description    : ������
 * Input          : None
 * Output         : None
 * Return         : None
 *******************************************************************************/
int main(void)
{
    // ===== ����1: ����ϵͳ��ʼ�� =====
    PowerMonitor(ENABLE, LPLevel_2V5);
    PFIC_EnableIRQ(WDOG_BAT_IRQn);
#if (defined (DCDC_ENABLE)) && (DCDC_ENABLE == TRUE)
    PWR_DCDCCfg(ENABLE);
#endif

    // ===== ����1.5: ��ʼ�����Դ��ڣ������ڼ�鸴λ��־ǰ�� =====
#ifdef DEBUG
    GPIOA_SetBits(bTXD1);
    GPIOA_ModeCfg(bTXD1, GPIO_ModeOut_PP_5mA);
    UART1_DefInit();
    UART1_BaudRateCfg(115200);
#endif
    DEBUG_Init();

    // ===== 步骤2: 读取复位原因并决定工作模式 =====
    PRINT("=== SYSTEM STARTUP ===\n");
    PRINT("Reading reset status register...\n");

    uint8_t reset_flag = get_reset_flag();

    PRINT("Reset flag: 0x%02X\n", reset_flag);
    PRINT("R8_RESET_STATUS register: 0x%02X\n", R8_RESET_STATUS);

    // 添加复位状态诊断
    diagnose_reset_status();

    // 睡眠逻辑控制开关，调试完成后改为0，启用睡眠
    #define SKIP_SLEEP_FOR_DEBUG 0

    #if SKIP_SLEEP_FOR_DEBUG
    PRINT("=== DEBUG MODE - SKIPPING SLEEP LOGIC ===\n");
    // ��ʼ��LED���ڲ���
    power_led_init();
    power_led_blink_once();  // ����LED�Ƿ���
    PRINT("LED test completed, continuing with normal startup...\n");
    #else

    if (is_power_on_reset()) {
        // ----------------------------------------------------
        // �״��ϵ���Դ�������� - LEDָʾ�����˯��ģʽ
        // ----------------------------------------------------
        PRINT("\n=== POWER ON RESET DETECTED ===\n");
        PRINT("This is a fresh power-on, will enter sleep mode after LED indication\n");

        // ��ʼ��LED
        power_led_init();

        // �ϵ�ָʾ��LED��0.5��
        power_led_power_on_indicate();

        // ���û��Ѱ���
        configure_wakeup_keys();

        // �����µ�ģʽ
        enter_shutdown_mode();

        // ע�⣺����ִ�е�����ͻ�ֹͣ��ֱ�������Ѳ���λ

    } else if (is_shutdown_wakeup_reset()) {
        // ----------------------------------------------------
        // 下电模式唤醒 - 执行唤醒后的处理逻辑
        // ----------------------------------------------------
        PRINT("\n=== SHUTDOWN WAKEUP RESET DETECTED ===\n");
        PRINT("Device was woken up from sleep mode by key press\n");

        // 初始化LED并显示唤醒指示
        PRINT("Step 1: Initializing power LED...\n");
        power_led_init();
        PRINT("Step 1: Power LED initialized successfully\n");

        // 唤醒指示：LED闪烁两次
        PRINT("Step 2: Starting wakeup LED indication...\n");
        power_led_wakeup_indicate();
        PRINT("Step 2: Wakeup LED indication completed\n");

        PRINT("Step 3: About to resume normal operation...\n");

        // 喂狗，防止看门狗复位
        WWDG_SetCounter(0xFF);  // 重载看门狗计数器
        DelayMs(100);  // 确保串口输出完成

        // 分段打印，避免可能的串口缓冲区问题
        PRINT("=== RESUMING ");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(10);
        PRINT("NORMAL ");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(10);
        PRINT("OPERATION ===\n");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(50);

        PRINT("Step 4: Normal operation resumed, continuing initialization...\n");

        // 添加更多调试信息，检查是否在这里出现问题
        PRINT("Step 4.1: About to continue with GPIO configuration...\n");
        DelayMs(50);
        WWDG_SetCounter(0xFF);  // 喂狗

        PRINT("Step 4.2: GPIO configuration completed, continuing...\n");
        DelayMs(50);
        WWDG_SetCounter(0xFF);  // 喂狗

        // 继续执行系统初始化...
        PRINT("Step 4.3: Proceeding to system initialization...\n");

    } else {
        // ----------------------------------------------------
        // 其他复位原因 - 可能是睡眠唤醒或其他复位
        // ----------------------------------------------------
        PRINT("\n=== OTHER RESET DETECTED ===\n");
        PRINT("Reset flag: 0x%02X (Watchdog/Software/Other reset)\n", reset_flag);

        // 检查是否可能是睡眠唤醒（通过检查GPIO状态或其他方式）
        // 这里我们假设所有非上电复位都可能是睡眠唤醒
        bool is_likely_sleep_wakeup = false;

        // 检查是否有按键被按下（可能是唤醒源）
        if (!GPIOA_ReadPortPin(GPIO_Pin_0) ||
            !GPIOB_ReadPortPin(GPIO_Pin_8) ||
            !GPIOB_ReadPortPin(GPIO_Pin_9)) {
            is_likely_sleep_wakeup = true;
            PRINT("Detected key press, likely sleep wakeup\n");
        }

        if (is_likely_sleep_wakeup) {
            PRINT("Likely sleep wakeup detected - showing wakeup indication\n");
            // 初始化LED并显示唤醒指示
            PRINT("Step 1: Initializing power LED for likely wakeup...\n");
            power_led_init();
            PRINT("Step 1: Power LED initialized successfully\n");

            PRINT("Step 2: Starting wakeup LED indication for likely wakeup...\n");
            power_led_wakeup_indicate();
            PRINT("Step 2: Wakeup LED indication completed\n");
        } else {
            PRINT("Other reset type - normal LED initialization\n");
            // 初始化LED
            PRINT("Step 1: Initializing power LED for other reset...\n");
            power_led_init();
            PRINT("Step 1: Power LED initialized successfully\n");
        }

        PRINT("Step 3: About to continue normal operation...\n");

        // 喂狗，防止看门狗复位
        WWDG_SetCounter(0xFF);  // 重载看门狗计数器
        DelayMs(100);  // 确保串口输出完成

        // 分段打印，避免可能的串口缓冲区问题
        PRINT("=== CONTINUING ");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(10);
        PRINT("NORMAL ");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(10);
        PRINT("OPERATION ===\n");
        WWDG_SetCounter(0xFF);  // 喂狗
        DelayMs(50);

        PRINT("Step 4: Normal operation continuing, proceeding with initialization...\n");

        // 添加更多调试信息，检查是否在这里出现问题
        PRINT("Step 4.1: About to continue with GPIO configuration...\n");
        DelayMs(50);
        WWDG_SetCounter(0xFF);  // 喂狗

        PRINT("Step 4.2: GPIO configuration completed, continuing...\n");
        DelayMs(50);
        WWDG_SetCounter(0xFF);  // 喂狗

        // 继续执行系统初始化...
        PRINT("Step 4.3: Proceeding to system initialization...\n");
    }

    #endif  // SKIP_SLEEP_FOR_DEBUG
    // ========================================
    // 系统初始化 - 所有复位类型都需要执行
    // ========================================

    PRINT("\n=== STARTING SYSTEM INITIALIZATION ===\n");

    PRINT("Step 5: Starting EasyFlash initialization...\n");
    WWDG_SetCounter(0xFF);  // 喂狗
    if (easyflash_init() != SUCCESS) {
        LOG_INFO("Date Flash init error!");
    }
    PRINT("Step 5: EasyFlash initialization completed\n");

    PRINT("Step 6: Printing environment variables...\n");
    WWDG_SetCounter(0xFF);  // 喂狗
    ef_print_env();
    PRINT("Step 6: Environment variables printed\n");

    PRINT("Step 7: Reading device info...\n");
    WWDG_SetCounter(0xFF);  // 喂狗
    ReadDeviceInfo("all");  //must process after easyflash_init()
    PRINT("Step 7: Device info read completed\n");

    PRINT("Step 8: Initializing ring buffer...\n");
    WWDG_SetCounter(0xFF);  // 喂狗
    ring_buffer_init();
    PRINT("Step 8: Ring buffer initialized\n");

    LOG_INFO("device id:%#x", device_bond.ID_Num);

    // 设备模式调试信息
    PRINT("=== DEVICE MODE DEBUG ===\n");
    PRINT("device_mode = 0x%02X (", device_mode);
    switch(device_mode) {
        case MODE_BLE:
            PRINT("BLE");
            break;
        case MODE_USB:
            PRINT("USB");
            break;
        case MODE_RF24:
            PRINT("RF24");
            break;
        case MODE_TSET:
            PRINT("TEST");
            break;
        default:
            PRINT("UNKNOWN");
            break;
    }
    PRINT(")\n");
    PRINT("=== END DEVICE MODE DEBUG ===\n");

    PRINT("Step 9: Starting Mode_Init with device_mode = 0x%02X...\n", device_mode);
    WWDG_SetCounter(0xFF);  // 喂狗
    Mode_Init(device_mode);
    PRINT("Step 9: Mode_Init completed successfully\n");

    PRINT("Step 10: Starting HAL_ADC_EVENT task...\n");
    WWDG_SetCounter(0xFF);  // 喂狗
    tmos_start_task( halTaskID, HAL_ADC_EVENT, MS1_TO_SYSTEM_TIME(6*1000));   //1min 测一次电池电压
    PRINT("Step 10: HAL_ADC_EVENT task started\n");

    // 检查是否需要显示唤醒LED指示（用于无操作进入睡眠后的唤醒）
    PRINT("Step 11: Checking for sleep wakeup LED indication...\n");
    if (pm_should_show_wakeup_led()) {
        PRINT("=== SLEEP WAKEUP DETECTED - SHOWING LED INDICATION ===\n");
        power_led_wakeup_indicate();
        PRINT("Sleep wakeup LED indication completed\n");
    } else {
        PRINT("No sleep wakeup LED indication needed\n");
    }
    PRINT("Step 11: Sleep wakeup check completed\n");

    // 强制测试电池检测功能
    PRINT("=== FORCED BATTERY TEST ===\n");
    // 等待一段时间让系统初始化完成
    DelayMs(2000);

    // 首先测试PA13引脚的基本状态
    PRINT("=== PA13 PIN TEST ===\n");

    // 测试上拉状态
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_PU);  // 配置为上拉输入
    DelayMs(100);  // 等待稳定
    uint8_t pin_state_pu = GPIOA_ReadPortPin(GPIO_Pin_13) ? 1 : 0;
    PRINT("PA13 with pull-up: %d\n", pin_state_pu);

    // 测试下拉状态
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_PD);  // 配置为下拉输入
    DelayMs(100);  // 等待稳定
    uint8_t pin_state_pd = GPIOA_ReadPortPin(GPIO_Pin_13) ? 1 : 0;
    PRINT("PA13 with pull-down: %d\n", pin_state_pd);

    // 测试浮空状态
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_Floating);  // 配置为浮空输入
    DelayMs(200);  // 等待RC电路稳定
    uint8_t pin_state_float = GPIOA_ReadPortPin(GPIO_Pin_13) ? 1 : 0;
    PRINT("PA13 floating: %d\n", pin_state_float);

    // 硬件连接诊断
    if (pin_state_pu == 1 && pin_state_pd == 1 && pin_state_float == 1) {
        PRINT("⚠️  PA13 always HIGH - likely connected to VDD!\n");
        PRINT("Hardware issue: PA13 may not be connected to battery divider circuit\n");
    } else if (pin_state_pu == 0 && pin_state_pd == 0 && pin_state_float == 0) {
        PRINT("⚠️  PA13 always LOW - likely connected to GND!\n");
    } else {
        PRINT("✅ PA13 responds to pull-up/down - hardware connection seems OK\n");
        PRINT("Expected behavior: pull-up=1, pull-down=0, floating=intermediate\n");
    }

    // 最终设置为浮空模式，为后续ADC测量做准备
    GPIOA_ModeCfg(GPIO_Pin_13, GPIO_ModeIN_Floating);
    PRINT("PA13 set to floating mode for ADC measurements\n");
    PRINT("=== END PA13 PIN TEST ===\n");

    // 尝试直接调用电池检测（如果电池服务已初始化）
    extern bStatus_t Batt_AddService(void);
    extern uint8_t Batt_MeasLevel(void);

    PRINT("Attempting to initialize battery service directly...\n");
    bStatus_t batt_status = Batt_AddService();
    PRINT("Battery service initialization result: %d\n", batt_status);

    // 使用新的统一电池管理函数进行测试
    PRINT("=== TESTING UNIFIED BATTERY MANAGEMENT ===\n");
    extern void MeasureAndReportBatteryLevel(void);
    PRINT("Calling unified battery measurement function...\n");
    MeasureAndReportBatteryLevel();
    
    // 测试强制启用通知功能
    PRINT("\n=== TESTING FORCE ENABLE NOTIFICATIONS ===\n");
    extern void Batt_ForceEnableNotifications(void);
    PRINT("Attempting to force enable battery notifications...\n");
    Batt_ForceEnableNotifications();
    PRINT("=== END FORCE ENABLE TEST ===\n");
    
    PRINT("=== END UNIFIED BATTERY TEST ===\n");

    // 计算理论ADC值 (使用正确的1.05V参考电压)
    PRINT("=== THEORETICAL CALCULATION (Updated) ===\n");
    PRINT("Battery voltage: 3.8V\n");
    PRINT("After 1:1 divider: 1.9V\n");
    PRINT("ADC Vref = 1.05V: Expected ADC = 1.9/1.05*4096 = %d (saturated)\n", (int)(1.9/1.05*4096));
    PRINT("This explains why we see ADC readings around 3770-3780\n");
    PRINT("=== END THEORETICAL CALCULATION ===\n");

    PRINT("=== END FORCED BATTERY TEST ===\n");

    PRINT("Step 12: About to enter Main_Circulation...\n");
    DelayMs(100);  // 确保串口输出完成
    PRINT("=== ENTERING MAIN CIRCULATION LOOP ===\n");
    Main_Circulation();





#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    // 配置大部分GPIO为下拉输入以降低功耗，但排除唤醒按键和LED引脚
    // PA0是唤醒按键E，需要保持上拉
    GPIOA_ModeCfg( GPIO_Pin_All & ~GPIO_Pin_0, GPIO_ModeIN_PD);
    // PB8、PB9是唤醒按键Q、R，PB12是电源LED引脚，需要特殊处理
    GPIOB_ModeCfg( GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_12), GPIO_ModeIN_PD);

    // 配置唤醒按键为上拉输入
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);  // E键
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);  // Q键和R键

    // 注释掉LED引脚配置，避免与电源LED系统冲突
    // 电源LED系统会自己管理PB12引脚
    // GPIOB_ModeCfg(GPIO_Pin_12, GPIO_ModeOut_PP_5mA);
    // GPIOB_ResetBits(GPIO_Pin_12);
#endif

    // ���ڳ�ʼ������ǰ�����
#ifdef CONFIG_RF_DEBUG_GPIO
    GPIOA_SetBits(GPIO_Pin_0 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6);
    GPIOA_ModeCfg(GPIO_Pin_0 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6, GPIO_ModeOut_PP_5mA);
#endif

//    GPIOB_ResetBits(GPIO_Pin_23);
//        GPIOB_ModeCfg( GPIO_Pin_23, GPIO_ModeOut_PP_5mA);

//    GPIOA_ModeCfg(GPIO_Pin_15, GPIO_ModeIN_PD);
//    GPIOA_SetBits(GPIO_Pin_14);
//    GPIOA_ModeCfg( GPIO_Pin_14, GPIO_ModeOut_PP_5mA);


//    if(GPIOA_ReadPortPin(GPIO_Pin_15)){
//        GPIOA_ResetBits(GPIO_Pin_14);
//        isUSBinsert = true;
//        PRINT("USB insert\n");
//    } else{
//        GPIOA_ModeCfg( GPIO_Pin_14, GPIO_ModeIN_Floating);
//        isUSBinsert = false;
//        PRINT("NO USB\n");
//    }







}

__HIGH_CODE
void WDOG_BAT_IRQHandler(void)
{
    FLASH_ROM_SW_RESET();

	uint32_t mcause;
	__asm__ volatile("csrr %0, mcause" : "=r" (mcause));

	uint32_t mtval;
	__asm__ volatile("csrr %0, mtval" : "=r" (mtval));

	mcause &= 0x1f;

    PRINT("\n!!! WDOG_BAT_IRQHandler TRIGGERED !!!\n");
    PRINT("This explains the system reset!\n");
	PRINT("mcause: %ld\n", mcause);
	PRINT("mtval: %lx\n", mtval);
	PRINT("Battery status: 0x%02X\n", R8_BAT_STATUS);
	DelayMs(10);

    while (1){
        static uint32_t i = 0;
        if(R8_BAT_STATUS & RB_BAT_STAT_LOW){
            i = 0;
        } else {
            i++;
            if(i > 100){
                break;
            }
        }
        DelayMs(1);
    }

    SYS_ResetExecute();
    __builtin_unreachable();
}
//���ļ�¼������������#define HAL_ADC true,hidkbd.c��ر�������{1618}//scanRspData[]���Ϊ0x45,,HidEmu_Init()��ĵ�����ùرգ�
//uint8 battMeasure( void )ȥ��ǰ���static����صȼ����������ˡ�HID_RPT_ID_KEY_IN��GPIO_Pin_23,RST,CH582����RST,CH582_RST_to_GPIO
//GPIO_Pin_2,����Ҫȥ����λ�ţ�HID_RPT_ID_KEY_IN��conn param updata��ble_chan_1: long,  B��ALT R��λPB12
//GPIO_Pin_12Initialized..battery low���ر���ʶ��USB��Enter pm idle��Status:hidEmu_ProcessTMOSMsg
//conn param updata:WAKE,HID_RPT_ID_KEY_IN,IRQHandler��IRQ GPIO_Pin_6,Enter pm idle,param updata:
//conn param updata:Enter PM standby!
//Fn ����ģʽ Idle��1.6mASLEEPDEEP���� POWER_PLAN��  GPIOA_ITModeCfg,GPIOA_ClearITFlagBit


///*DataFlash��ʼ��*/
//    GPIOA_SetBits(GPIO_Pin_All);
//    GPIOA_ResetBits(GPIO_Pin_All);
//     DelayMs(10);
//
////        usb_disable();
//     GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6��������  pa13��������     ,pb11��������pb10��������pb12��������
//     GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);//PB10,-15����Ϊ200ua//0-5wei63.8,6-9,16,17wei63.8//
////         GPIOA_ModeCfg(
////                 GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6,
////                 GPIO_ModeIN_PU);
////        GPIOB_ModeCfg(
////                        GPIO_Pin_12,
////                       GPIO_ModeIN_PU);
//
////        PFIC_EnableIRQ( GPIO_A_IRQn );
////    PRINT("shut down mode sleep \n");
// DelayMs(5000);
// PRINT("shut down mode sleep \n");
//    DelayMs(2);
//    LowPower_Shutdown(0); //ȫ���ϵ磬���Ѻ�λ




/*DataFlash��ʼ��*/
//
//DelayMs(10);
//
////        usb_disable();
//GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6��������  pa13��������     ,pb11��������pb10��������pb12��������
//GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);//PB10,-15����Ϊ200ua//0-5wei63.8,6-9,16,17wei63.8//
// GPIOA_ModeCfg(
//         GPIO_Pin_0 | GPIO_Pin_1 | GPIO_Pin_2 | GPIO_Pin_3 | GPIO_Pin_4 | GPIO_Pin_5 | GPIO_Pin_6,
//         GPIO_ModeIN_PU);
//GPIOB_ModeCfg(
//                GPIO_Pin_12,
//               GPIO_ModeIN_PU);
//
////        PFIC_EnableIRQ( GPIO_A_IRQn );
////    PRINT("shut down mode sleep \n");
//DelayMs(5000);
//PRINT("shut down mode sleep \n");
//DelayMs(2);
//LowPower_Shutdown(0); //ȫ���ϵ磬���Ѻ�λ

////    PM_DBG("Enter PM standby!\n");
//       DelayMs(10);
//           GPIOA_ResetBits(GPIO_Pin_All);
//           GPIOB_ResetBits(GPIO_Pin_All);
//            DelayMs(10);
//            GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//pa0,1,4,5,6��������  pa13��������     ,pb11.pb10��������pb12��������
//            GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PU);//PB10,-15����Ϊ200ua//0-5wei63.8,6-9,16,17wei63.8//
//               GPIOB_ModeCfg( GPIO_Pin_14, GPIO_ModeOut_PP_5mA);
//               GPIOB_ResetBits(GPIO_Pin_14);
//               PFIC_EnableIRQ( GPIO_A_IRQn );
//          DelayMs(2);
//          LowPower_Shutdown(0); //ȫ���ϵ磬���Ѻ�λ


/******************************** endfile @ main ******************************/
