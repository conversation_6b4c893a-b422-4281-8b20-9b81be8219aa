# 睡眠唤醒LED指示实现说明

## 功能概述
实现睡眠模式唤醒后LED闪烁两下的功能，包括：
1. 上电后的第一次睡眠唤醒
2. 后续所有因为无操作进入睡眠模式的唤醒

## 实现原理

### 1. **睡眠状态跟踪**
在 `subsys/PM/pm_task.c` 中添加了睡眠状态跟踪机制：

```c
// 添加睡眠状态跟踪
static bool was_in_sleep_mode = false;
static bool should_show_wakeup_led = false;

// 检查是否需要显示唤醒LED指示
bool pm_should_show_wakeup_led(void)
{
    bool result = should_show_wakeup_led;
    should_show_wakeup_led = false;  // 清除标志，避免重复显示
    return result;
}

// 设置睡眠状态
void pm_set_sleep_state(bool in_sleep)
{
    was_in_sleep_mode = in_sleep;
    if (!in_sleep && was_in_sleep_mode) {
        // 从睡眠模式唤醒
        should_show_wakeup_led = true;
        PM_DBG("Wakeup detected, will show LED indication\n");
    }
}
```

### 2. **进入睡眠时设置状态**
在 `pm_goto_powerdown()` 函数中设置睡眠状态：

```c
void pm_goto_powerdown(void)
{
    PM_DBG("Enter PM powerdown mode!\n");
    
    // 设置睡眠状态
    pm_set_sleep_state(true);
    
    // ... 其他睡眠配置
}
```

### 3. **唤醒后LED指示检查**
在 `APP/src/main.c` 的系统初始化完成后检查是否需要显示唤醒LED：

```c
// 检查是否需要显示唤醒LED指示（用于无操作进入睡眠后的唤醒）
if (pm_should_show_wakeup_led()) {
    PRINT("=== SLEEP WAKEUP DETECTED - SHOWING LED INDICATION ===\n");
    power_led_wakeup_indicate();
}
```

## 工作流程

### **完整流程：**
```
1. 系统启动 → 正常工作
2. 1分钟无操作 → pm_goto_powerdown() → pm_set_sleep_state(true)
3. 进入深度睡眠 → LowPower_Shutdown(0)
4. 按键唤醒 → 系统复位
5. 复位检测 → 判断复位类型
6. 系统初始化 → 检查pm_should_show_wakeup_led()
7. 显示唤醒指示 → power_led_wakeup_indicate() → LED闪烁两次
8. 继续正常工作
```

### **LED指示时机：**
- **上电复位**：LED亮0.5秒后进入睡眠
- **睡眠唤醒复位**：LED闪烁两次
- **无操作睡眠唤醒**：LED闪烁两次

## 文件修改总结

### **修改的文件：**

1. **`subsys/PM/pm_task.h`**
   - 添加 `pm_should_show_wakeup_led()` 函数声明
   - 添加 `pm_set_sleep_state()` 函数声明

2. **`subsys/PM/pm_task.c`**
   - 添加睡眠状态跟踪变量
   - 实现睡眠状态管理函数
   - 在进入睡眠时设置状态

3. **`APP/src/main.c`**
   - 在系统初始化完成后检查唤醒LED指示
   - 改进复位类型判断逻辑

4. **`drivers/power_led/sleep_manager.h`**
   - 添加测试函数声明

5. **`drivers/power_led/sleep_manager.c`**
   - 添加测试睡眠唤醒LED指示功能

## 使用方法

### **基本功能：**
- 系统会自动检测睡眠唤醒并显示LED指示
- 无需手动调用，完全自动化

### **测试功能：**
```c
// 测试睡眠唤醒LED指示
test_sleep_wakeup_led();

// 测试完整LED功能
power_led_test();
```

## 技术特点

### **优势：**
1. **自动化**：无需手动管理，系统自动检测唤醒
2. **可靠性**：通过状态跟踪确保不遗漏唤醒事件
3. **一致性**：所有睡眠唤醒都显示相同的LED指示
4. **调试友好**：提供详细的调试信息和测试功能

### **注意事项：**
1. **状态清除**：每次检查后自动清除标志，避免重复显示
2. **时序控制**：在系统初始化完成后检查，确保LED系统已就绪
3. **兼容性**：不影响现有的上电和唤醒逻辑

## 测试验证

### **测试步骤：**
1. 编译并下载程序
2. 等待1分钟无操作，观察是否进入睡眠
3. 按键唤醒，观察LED是否闪烁两次
4. 重复测试，确认每次唤醒都有LED指示

### **预期结果：**
- 每次从睡眠模式唤醒后，LED都会闪烁两次
- 包括上电后的第一次唤醒和后续无操作进入睡眠的唤醒
- 串口输出相应的调试信息

## 完成状态

✅ 睡眠状态跟踪机制实现完成  
✅ 唤醒LED指示自动检测完成  
✅ 所有睡眠唤醒LED指示统一完成  
✅ 测试功能添加完成  
✅ 文档说明完成  

现在系统会在所有睡眠唤醒后自动显示LED闪烁指示！
