#include "sleep_manager.h"

// 读取复位标志
uint8_t get_reset_flag(void)
{
    return R8_RESET_STATUS & RESET_FLAG_MASK;
}

// 判断是否为上电复位
bool is_power_on_reset(void)
{
    return (get_reset_flag() == RESET_FLAG_RPOR);
}

// 判断是否为下电模式唤醒复位
bool is_shutdown_wakeup_reset(void)
{
    return (get_reset_flag() == RESET_FLAG_GRWSM);
}

// 复位状态诊断函数
void diagnose_reset_status(void)
{
    uint8_t reset_flag = get_reset_flag();
    uint8_t full_status = R8_RESET_STATUS;
    
    PRINT("\n=== RESET STATUS DIAGNOSIS ===\n");
    PRINT("Full R8_RESET_STATUS: 0x%02X\n", full_status);
    PRINT("Reset flag (masked): 0x%02X\n", reset_flag);
    
    if (reset_flag == RESET_FLAG_RPOR) {
        PRINT("Status: POWER ON RESET detected\n");
    } else if (reset_flag == RESET_FLAG_GRWSM) {
        PRINT("Status: SHUTDOWN WAKEUP RESET detected\n");
    } else {
        PRINT("Status: OTHER RESET detected (Watchdog/Software/Other)\n");
        PRINT("This might explain why LED stays on!\n");
    }
    
    PRINT("=== END DIAGNOSIS ===\n\n");
}

// LED控制函数
void power_led_init(void)
{
    PRINT("Initializing power LED on PB12...\n");
    
    // 配置PB12为推挽输出
    GPIOB_ModeCfg(POWER_LED_PIN, GPIO_ModeOut_PP_5mA);
    
    // 确保LED初始状态为关闭（高电平，LED不亮）
    GPIOB_SetBits(POWER_LED_PIN);
    power_led_off();  // 调用函数再次确认关闭状态
    
    PRINT("Power LED initialized and turned OFF\n");
}

void power_led_on(void)
{
    // 低电平点亮LED
    GPIOB_ResetBits(POWER_LED_PIN);
}

void power_led_off(void)
{
    // 高电平关闭LED
    GPIOB_SetBits(POWER_LED_PIN);
}

// 通用闪烁一次
void power_led_blink_once(void)
{
    power_led_on();   // 点亮LED
    DelayMs(200);
    power_led_off();  // 关闭LED
    DelayMs(200);
}

// 上电指示：亮0.5秒
void power_led_power_on_indicate(void)
{
    PRINT("LED: Power on indication (0.5s)\n");
    power_led_on();   // 点亮LED
    DelayMs(500);     // 亮0.5秒
    power_led_off();  // 关闭LED
}

// 唤醒指示：闪烁两次
void power_led_wakeup_indicate(void)
{
    PRINT("LED: Wakeup indication (blink twice) - Starting...\n");

    // 在LED操作前检查电池状态
    PRINT("Battery status before LED: 0x%02X\n", R8_BAT_STATUS);

    for(int i = 0; i < 2; i++) {
        PRINT("LED: Blink %d/2 - Turning ON\n", i+1);
        power_led_on();   // 点亮LED

        // 在延时期间喂狗，防止看门狗复位
        for(int j = 0; j < 20; j++) {
            DelayMs(10);
            // 喂狗操作 - 重载看门狗计数器
            WWDG_SetCounter(0xFF);
        }

        PRINT("LED: Blink %d/2 - Turning OFF\n", i+1);
        power_led_off();  // 关闭LED

        // 在延时期间继续喂狗
        for(int j = 0; j < 20; j++) {
            DelayMs(10);
            WWDG_SetCounter(0xFF);
        }

        // 检查电池状态
        PRINT("Battery status after blink %d: 0x%02X\n", i+1, R8_BAT_STATUS);
    }
    PRINT("LED: Wakeup indication completed successfully\n");
}

// 配置唤醒按键
void configure_wakeup_keys(void)
{
    PRINT("Configuring wakeup keys...\n");

    // 配置E键 (PA0) - 上拉输入，下降沿唤醒
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);
    GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge);  // 配置下降沿中断
    PRINT("PA0 configured as pull-up input with falling edge interrupt\n");

    // 配置Q键 (PB8) 和 R键 (PB9) - 上拉输入，下降沿唤醒
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);
    GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge);  // 配置下降沿中断
    PRINT("PB8, PB9 configured as pull-up input with falling edge interrupt\n");

    // 清除GPIO中断标志
    GPIOA_ClearITFlagBit(GPIO_Pin_0);
    GPIOB_ClearITFlagBit(GPIO_Pin_8 | GPIO_Pin_9);
    PRINT("GPIO interrupt flags cleared\n");

    // 使能GPIO中断
    PFIC_EnableIRQ(GPIO_A_IRQn);
    PFIC_EnableIRQ(GPIO_B_IRQn);
    PRINT("GPIO interrupts enabled\n");

    // 配置GPIO唤醒源
    PWR_PeriphWakeUpCfg(ENABLE, RB_SLP_GPIO_WAKE, Long_Delay);
    PRINT("GPIO wakeup source configured\n");

    PRINT("Wakeup keys configured: PA0(E), PB8(Q), PB9(R)\n");
}

// 测试睡眠唤醒LED指示功能
void test_sleep_wakeup_led(void)
{
    PRINT("\n=== TESTING SLEEP WAKEUP LED INDICATION ===\n");
    
    // 模拟从睡眠模式唤醒
    PRINT("Simulating sleep wakeup...\n");
    
    // 显示唤醒指示
    power_led_wakeup_indicate();
    
    PRINT("Sleep wakeup LED indication test completed\n");
}

// LED测试函数 - 用于验证LED控制功能
void power_led_test(void)
{
    PRINT("\n=== LED TEST START ===\n");
    
    // 测试1：点亮LED
    PRINT("Test 1: Turn on LED\n");
    power_led_on();
    DelayMs(1000);
    
    // 测试2：关闭LED
    PRINT("Test 2: Turn off LED\n");
    power_led_off();
    DelayMs(1000);
    
    // 测试3：闪烁一次
    PRINT("Test 3: Blink once\n");
    power_led_blink_once();
    DelayMs(1000);
    
    // 测试4：上电指示
    PRINT("Test 4: Power on indication\n");
    power_led_power_on_indicate();
    DelayMs(1000);
    
    // 测试5：唤醒指示
    PRINT("Test 5: Wakeup indication\n");
    power_led_wakeup_indicate();
    DelayMs(1000);
    
    PRINT("=== LED TEST COMPLETED ===\n");
}

// 进入下电模式
void enter_shutdown_mode(void)
{
    PRINT("\n=== ENTERING SHUTDOWN MODE ===\n");
    PRINT("Preparing to enter shutdown mode...\n");

    // 注释掉关闭LED的代码，避免进入睡眠时LED闪烁
    // power_led_off();
    // PRINT("LED turned off\n");

    // 配置所有GPIO为低功耗状态
    GPIOA_ResetBits(GPIO_Pin_All);
    GPIOB_ResetBits(GPIO_Pin_All);
    DelayMs(10);

    // 配置大部分GPIO为下拉输入以降低功耗，但排除唤醒按键和LED引脚
    // PA0是唤醒按键，需要保持上拉
    GPIOA_ModeCfg(GPIO_Pin_All & ~GPIO_Pin_0, GPIO_ModeIN_PD);
    // PB8、PB9是唤醒按键，PB12是LED引脚，需要特殊处理
    GPIOB_ModeCfg(GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_12), GPIO_ModeIN_PD);

    // 配置LED引脚为推挽输出并确保关闭状态（高电平，LED不亮）
    GPIOB_ModeCfg(GPIO_Pin_12, GPIO_ModeOut_PP_5mA);
    // 注释掉LED状态设置，避免进入睡眠时LED闪烁
    GPIOB_SetBits(GPIO_Pin_12);  // 修复：使用SetBits确保LED关闭

    // 配置唤醒按键
    configure_wakeup_keys();

    // 断开蓝牙连接（如果已连接）
    // 这里可以添加蓝牙断开的代码

    DelayMs(50);  // 确保所有配置生效

    PRINT("*** SYSTEM ENTERING SLEEP MODE ***\n");
    PRINT("*** Press E/Q/R keys to wake up ***\n");
    DelayMs(100);  // 确保串口输出完成

    // 进入下电模式，保持RAM以便调试
    LowPower_Shutdown(0);
    
    // 如果执行到这里，说明进入睡眠失败
    PRINT("ERROR: Failed to enter shutdown mode!\n");
    while(1) {
        DelayMs(1000);
        PRINT("Sleep failed, retrying...\n");
    }
}
