# 设备进入睡眠无日志输出问题分析报告

## 问题描述
用户反馈每次设备进入睡眠并没有输出相应的日志，需要检查原因。

## 问题分析

### **1. 日志输出机制分析**

#### **PM_DBG宏定义**
在 `subsys/PM/pm_task.c` 中：
```c
#define CONFIG_PM_DBG

#ifdef CONFIG_PM_DBG
#define PM_DBG  PRINT
#else
#define PM_DBG(...)
#endif
```

**结论：** `PM_DBG` 宏已正确定义为 `PRINT`，应该能正常输出日志。

#### **睡眠相关日志位置**
在 `pm_goto_powerdown()` 函数中应该输出的日志：
```c
PM_DBG("Enter PM powerdown mode!\n");
PM_DBG("Configured wakeup pins: PA0(E), PB8(Q), PB9(R)\n");
```

在 `pm_event()` 函数中应该输出的日志：
```c
PM_DBG("1 minute timeout - entering powerdown mode\n");
```

### **2. 可能的原因分析**

#### **原因1：设备没有真正进入睡眠模式**
- **现象：** 设备可能没有触发1分钟无操作超时
- **检查点：** 确认 `PM_ENTER_IDLE_EVENT` 是否被触发

#### **原因2：睡眠功能被禁用**
- **检查点：** 确认 `HAL_SLEEP` 是否被正确启用
- **当前状态：** `HAL_SLEEP = TRUE` ✅

#### **原因3：设备模式不匹配**
在 `pm_task_init()` 中：
```c
if(device_mode == MODE_USB || device_mode == MODE_TSET){
    // USB和测试模式不启用下电模式
    pm_start_working(PM_TIMEOUT_FOREVER, PM_TIMEOUT_FOREVER);
} else {
    // BLE和RF模式：1分钟无按键后进入深度下电模式
    pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
}
```

**问题：** 如果设备当前是USB模式或测试模式，睡眠功能会被禁用！

#### **原因4：按键扫描干扰**
在 `drivers/key_scan/keyscan.c` 中：
```c
pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
```

**问题：** 每次按键都会重置睡眠计时器，如果设备一直有按键输入，永远不会进入睡眠。

### **3. 调试建议**

#### **步骤1：确认设备模式**
在 `main.c` 中添加日志：
```c
PRINT("Current device mode: %d\n", device_mode);
PRINT("MODE_BLE=%d, MODE_USB=%d, MODE_TSET=%d\n", MODE_BLE, MODE_USB, MODE_TSET);
```

#### **步骤2：确认睡眠任务初始化**
在 `pm_task_init()` 中添加日志：
```c
PRINT("PM task initialized, device_mode=%d\n", device_mode);
if(device_mode == MODE_USB || device_mode == MODE_TSET){
    PRINT("USB/Test mode - sleep disabled\n");
    pm_start_working(PM_TIMEOUT_FOREVER, PM_TIMEOUT_FOREVER);
} else {
    PRINT("BLE/RF mode - sleep enabled, timeout=%d ms\n", PM_WORKING_TIMEOUT);
    pm_start_working(PM_WORKING_TIMEOUT, PM_TIMEOUT_FOREVER);
}
```

#### **步骤3：监控睡眠事件**
在 `pm_event()` 中添加更多日志：
```c
if(events & PM_ENTER_IDLE_EVENT) {
    PRINT("=== PM_ENTER_IDLE_EVENT triggered ===\n");
    PM_DBG("1 minute timeout - entering powerdown mode\n");
    pm_goto_powerdown();
    return (events ^ PM_ENTER_IDLE_EVENT);
}
```

#### **步骤4：检查按键输入**
在 `keyscan.c` 中添加日志：
```c
void pm_start_working(int working_timeout, int idle_timeout)
{
    PRINT("pm_start_working called: working=%d, idle=%d\n", working_timeout, idle_timeout);
    // ... 现有代码
}
```

### **4. 最可能的原因**

#### **优先级1：设备模式问题**
- 如果设备当前是USB模式，睡眠功能完全被禁用
- 需要检查 `device_mode` 变量的值

#### **优先级2：按键干扰**
- 如果设备一直有按键输入（包括矩阵扫描），睡眠计时器会不断重置
- 需要确认按键扫描是否正常工作

#### **优先级3：睡眠任务未启动**
- 如果 `pm_task_init()` 没有被调用，睡眠功能不会工作
- 需要确认HAL初始化流程

### **5. 立即检查项**

#### **检查1：设备模式**
```c
// 在main.c的Mode_Init之后添加
PRINT("Device mode after Mode_Init: %d\n", device_mode);
```

#### **检查2：睡眠任务状态**
```c
// 在pm_task_init中添加
PRINT("PM task ID: %d\n", pm_task_id);
```

#### **检查3：超时设置**
```c
// 在pm_start_working中添加
PRINT("Setting sleep timeout: %d ms\n", working_timeout);
```

### **6. 预期结果**

#### **正常情况：**
1. 设备启动后显示当前模式
2. PM任务初始化成功
3. 1分钟后显示进入睡眠的日志
4. 显示配置唤醒引脚的日志

#### **异常情况：**
1. 设备模式不匹配（USB/测试模式）
2. 按键扫描异常，不断重置计时器
3. PM任务初始化失败
4. 睡眠事件未被触发

## 建议解决方案

### **立即行动：**
1. 添加设备模式检查日志
2. 添加PM任务初始化状态日志
3. 添加睡眠事件触发日志
4. 确认设备当前工作模式

### **长期优化：**
1. 增加睡眠功能的调试开关
2. 添加睡眠状态监控
3. 优化按键扫描逻辑，避免误触发

## 总结

设备进入睡眠无日志输出的最可能原因是：
1. **设备当前是USB模式** - 睡眠功能被完全禁用
2. **按键扫描异常** - 睡眠计时器不断被重置
3. **PM任务未正确初始化** - 睡眠功能未启动

建议立即添加调试日志来确认具体原因。
