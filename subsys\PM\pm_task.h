/*
 * Copyright (c) 2022 zerosensei
 *
 * SPDX-License-Identifier: Apache-2.0
 */

#ifndef SUBSYS_PM_PM_TASK_H
#define SUBSYS_PM_PM_TASK_H

#include "soc.h"
#include "config.h"

enum pm_state{
  PM_STATE_ACTIVE,
  PM_STATE_SLEEP,
  PM_STATE_SHUT,

  PM_STATE_NUM
};

/* task events */
#define PM_ENTER_IDLE_EVENT           (1<<1)
#define PM_ENTER_STANDBY_EVENT        (1<<2)

#define PM_TIMEOUT_FOREVER              (-1)
#define PM_WORKING_TIMEOUT              (1000*60*10)    // 10分钟无按键进入下电模式
#define PM_IDLE_TIMEOUT                 PM_TIMEOUT_FOREVER

// 新增深度下电模式事件
#define PM_ENTER_POWERDOWN_EVENT        (1<<3)

void pm_task_init(void);
u8 pm_is_in_idle(void);
bool pm_should_show_wakeup_led(void);  // 检查是否需要显示唤醒LED指示萨德我阿1sradr
void pm_set_sleep_state(bool in_sleep); // 设置睡眠状态
void pm_start_working(int working_timeout, int idle_timeout);
void pm_goto_standby(void);
void pm_goto_powerdown(void);  // 新增深度下电模式函数

#endif /* SUBSYS_PM_PM_TASK_H */
