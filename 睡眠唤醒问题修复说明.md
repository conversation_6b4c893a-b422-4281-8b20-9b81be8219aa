# 睡眠唤醒问题修复说明

## 问题描述
用户报告：上电后能进入睡眠，但是设备无法唤醒，而且LED在上电之后闪烁两次。

## 问题分析

### 1. 日志分析
从提供的日志可以看到：
```
=== POWER ON RESET DETECTED ===
This is a fresh power-on, will enter sleep mode after LED indication
LED: Power on indication (0.5s)
Configuring wakeup keys...
Wakeup keys configured: PA0(E), PB8(Q), PB9(R)
=== ENTERING SHUTDOWN MODE ===
```

- 复位标志是 `0x01`（上电复位），这是正确的
- 系统执行了上电指示（LED亮0.5秒）
- 但用户观察到LED闪烁两次，说明存在异常

### 2. 根本原因分析

#### 问题1：GPIO配置冲突
在 `APP/src/main.c` 第148-151行：
```c
#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
#endif
```

由于 `HAL_SLEEP` 在 `subsys/HAL/config.h` 中被定义为 `TRUE`，这段代码会被执行，导致：
- **PA0（E键）** 被配置为下拉输入，覆盖了唤醒按键配置
- **PB8、PB9（Q、R键）** 被配置为下拉输入，覆盖了唤醒按键配置  
- **PB12（LED引脚）** 被配置为下拉输入，可能导致LED状态异常

#### 问题2：睡眠模式中缺少GPIO中断配置
在 `drivers/power_led/sleep_manager.c` 的 `configure_wakeup_keys()` 函数中，缺少了：
- GPIO中断模式配置（`GPIOA_ITModeCfg`、`GPIOB_ITModeCfg`）
- GPIO中断使能（`PFIC_EnableIRQ`）

#### 问题3：进入睡眠时GPIO配置顺序问题
在 `enter_shutdown_mode()` 函数中，先把所有GPIO配置为下拉输入，然后再配置唤醒按键，但没有正确处理LED引脚。

## 修复方案

### 1. 修复main.c中的GPIO配置冲突
**文件**: `APP/src/main.c` 第148-162行

**修改前**:
```c
#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    GPIOA_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
    GPIOB_ModeCfg( GPIO_Pin_All, GPIO_ModeIN_PD);
#endif
```

**修改后**:
```c
#if (defined (HAL_SLEEP)) && (HAL_SLEEP == TRUE)
    // 配置大部分GPIO为下拉输入以降低功耗，但排除唤醒按键和LED引脚
    // PA0是唤醒按键E，需要保持上拉
    GPIOA_ModeCfg( GPIO_Pin_All & ~GPIO_Pin_0, GPIO_ModeIN_PD);
    // PB8、PB9是唤醒按键Q、R，PB12是LED引脚，需要特殊处理
    GPIOB_ModeCfg( GPIO_Pin_All & ~(GPIO_Pin_8 | GPIO_Pin_9 | GPIO_Pin_12), GPIO_ModeIN_PD);
    
    // 配置唤醒按键为上拉输入
    GPIOA_ModeCfg(GPIO_Pin_0, GPIO_ModeIN_PU);  // E键
    GPIOB_ModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ModeIN_PU);  // Q键、R键
    
    // 配置LED引脚为推挽输出并保持关闭状态
    GPIOB_ModeCfg(GPIO_Pin_12, GPIO_ModeOut_PP_5mA);
    GPIOB_ResetBits(GPIO_Pin_12);
#endif
```

### 2. 修复睡眠管理器中的GPIO中断配置
**文件**: `drivers/power_led/sleep_manager.c` 第69-99行

**添加了**:
- `GPIOA_ITModeCfg(GPIO_Pin_0, GPIO_ITMode_FallEdge)`
- `GPIOB_ITModeCfg(GPIO_Pin_8 | GPIO_Pin_9, GPIO_ITMode_FallEdge)`
- `PFIC_EnableIRQ(GPIO_A_IRQn)`
- `PFIC_EnableIRQ(GPIO_B_IRQn)`
- 详细的调试日志输出

### 3. 修复进入睡眠时的GPIO配置
**文件**: `drivers/power_led/sleep_manager.c` 第96-134行

**修改了**:
- 排除唤醒按键和LED引脚的GPIO配置
- 正确配置LED引脚为推挽输出并保持关闭状态
- 确保唤醒按键配置不被覆盖

## 修复效果

### 1. 解决LED闪烁两次问题
- LED引脚（PB12）不再被错误配置为下拉输入
- 上电指示只会执行一次（LED亮0.5秒）
- 进入睡眠后LED保持关闭状态

### 2. 解决无法唤醒问题
- 唤醒按键（PA0、PB8、PB9）正确配置为上拉输入
- GPIO中断正确配置为下降沿触发
- GPIO中断正确使能
- GPIO唤醒源正确配置

### 3. 改善调试体验
- 添加了详细的配置过程日志
- 便于问题排查和功能验证

## 测试验证

### 1. 上电测试
预期行为：
- 设备上电后输出启动日志
- LED亮0.5秒后熄灭（不是闪烁两次）
- 设备进入睡眠模式

### 2. 唤醒测试
预期行为：
- 按下E键（PA0）可以唤醒设备
- 按下Q键（PB8）可以唤醒设备
- 按下R键（PB9）可以唤醒设备
- 唤醒后LED闪烁两次，然后开始正常工作

### 3. 循环测试
预期行为：
- 唤醒后设备正常工作，不会再次自动进入睡眠
- 可以重复进行睡眠-唤醒循环测试

## 注意事项

1. **编译验证**: 修改后需要重新编译并烧录固件
2. **硬件连接**: 确保E、Q、R按键硬件连接正确
3. **调试输出**: 通过串口可以看到详细的配置过程日志
4. **功耗测试**: 建议测试睡眠模式下的实际功耗

## 相关文件

- `APP/src/main.c` - 主程序，修复了GPIO配置冲突
- `drivers/power_led/sleep_manager.c` - 睡眠管理器，修复了中断配置
- `drivers/power_led/sleep_manager.h` - 睡眠管理器头文件
- `subsys/HAL/config.h` - HAL配置文件（HAL_SLEEP=TRUE）
