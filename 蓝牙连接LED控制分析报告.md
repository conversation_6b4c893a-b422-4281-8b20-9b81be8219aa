# 蓝牙连接LED控制分析报告

## 概述
通过全面检查代码，发现蓝牙连接相关的程序确实有多处控制LED的代码，这些可能是导致LED异常闪烁的另一个重要原因。

## 🔍 蓝牙相关的LED控制

### 1. BLE HID键盘服务 (`subsys/BLE/hidkbd.c`)

#### 1.1 连接状态LED指示
**文件**: `subsys/BLE/hidkbd.c`

##### 广播状态 (GAPROLE_ADVERTISING)
```c
case GAPROLE_ADVERTISING:
    if (pEvent->gap.opcode == GAP_MAKE_DISCOVERABLE_DONE_EVENT) {
        PRINT("Advertising..\n");
        led_flash=1;  // 启动LED闪烁
        tmos_start_task( hidEmuTaskId, HAL_LEDFLASH_EVENT, MS1_TO_SYSTEM_TIME(1*1000));
        tmos_start_task( hidEmuTaskId, HAL_WAITOUT1_EVENT, MS1_TO_SYSTEM_TIME(120*1000));
    }
    break;
```

##### 连接成功状态 (GAPROLE_CONNECTED)
```c
case GAPROLE_CONNECTED:
    PRINT("Connected..\n");
    led_flash=0;      // 停止LED闪烁
    set_led_scr(1);   // 点亮Scroll Lock LED (PB12!)
    tmos_start_task( hidEmuTaskId, HAL_LED5s_EVENT, MS1_TO_SYSTEM_TIME(3*1000));
    tmos_stop_task( hidEmuTaskId, HAL_WAITOUT1_EVENT );
    break;
```

#### 1.2 LED闪烁事件处理
```c
if(events & HAL_LEDFLASH_EVENT)
{
    if(led_flash==1)
    {
        iled=!iled;
        set_led_scr(iled);  // 控制PB12引脚闪烁！
        tmos_start_task( hidEmuTaskId, HAL_LEDFLASH_EVENT, MS1_TO_SYSTEM_TIME(1*1000));
    }
    return (events ^ HAL_LEDFLASH_EVENT);
}
```

#### 1.3 LED定时事件处理
```c
if(events & HAL_LED5s_EVENT)
{
    led5scnt++;
    if(led5scnt==1)
    {
        set_led_scr(1);  // 点亮PB12
        tmos_start_task( hidEmuTaskId, HAL_LED5s_EVENT, MS1_TO_SYSTEM_TIME(3*1000));
    }
    if(led5scnt==2)
    {
        set_led_scr(0);  // 关闭PB12
        led5scnt=0;
    }
    return (events ^ HAL_LED5s_EVENT);
}
```

#### 1.4 HID LED输出报告处理
```c
static uint8 hidEmuRcvReport(uint8 len, uint8 *pData) {
    if (len == HID_LED_OUT_RPT_LEN) {
        // 根据主机发送的LED状态设置键盘LED
        (pData[0] & 0x01) ? set_led_num(1) : set_led_num(0);  // Num Lock
        (pData[0] & 0x02) ? set_led_cap(1) : set_led_cap(0);  // Caps Lock
        (pData[0] & 0x04) ? set_led_scr(1) : set_led_scr(0);  // Scroll Lock (PB12!)
        return SUCCESS;
    }
}
```

### 2. RF 2.4G通信 (`subsys/RF_task/rf_dev.c`)

#### 2.1 RF LED状态同步
```c
void rf_key_deal(uint8_t *data, uint8_t len)
{
    rf_data_t *tempbuf = (rf_data_t *)data;
    if(tempbuf->report_id == 0 && len == sizeof(rf_data_t) - sizeof(tempbuf->data) + 1) {
        // 根据接收到的数据设置LED状态
        (tempbuf->data[0] & 0x01 )?set_led_num(true):set_led_num(false);   // Num Lock
        (tempbuf->data[0] & 0x02 )?set_led_cap(true):set_led_cap(false);   // Caps Lock  
        (tempbuf->data[0] & 0x04 )?set_led_scr(true):set_led_scr(false);   // Scroll Lock (PB12!)
        LOG_INFO("get led: %#x", tempbuf->data[0]);
    }
}
```

### 3. HAL LED系统 (`subsys/HAL/LED.c`)

#### 3.1 RF配对状态LED
**文件**: `subsys/HAL/LED.h` 第54行
```c
#define LED1_BV           BV(7)   //TODO: rf pairing status LED
```

这个LED使用PA7引脚，用于RF配对状态指示。

## 🚨 **关键发现：引脚冲突确认**

### 冲突的引脚使用
1. **我们的电源LED**: 原来使用 `PB12` (现已改为PB14)
2. **蓝牙状态LED**: 使用 `PB12` (`LED_SCR` - Scroll Lock LED)
3. **背光系统**: 也使用 `PB12` (`LED_SCR` 和 `LED_CAP`)

### 冲突的函数调用
- `set_led_scr(1)` - 蓝牙连接时调用，控制PB12
- `set_led_scr(iled)` - 蓝牙广播时闪烁，控制PB12
- `bkinit()` - 背光初始化，也配置PB12

## 🔍 **LED闪烁两次的完整原因分析**

### 执行时序分析
1. **上电启动**:
   - `power_led_power_on_indicate()` → PB12亮0.5秒
   
2. **系统初始化**:
   - `bkinit()` → 重新配置PB12引脚
   
3. **蓝牙初始化**:
   - 如果设备模式是BLE，蓝牙开始广播
   - `GAPROLE_ADVERTISING` 状态 → `led_flash=1`
   - `HAL_LEDFLASH_EVENT` → `set_led_scr(iled)` 开始闪烁PB12
   
4. **结果**:
   - 用户看到LED"闪烁两次"，实际上是：
     - 第一次：电源指示（0.5秒亮）
     - 第二次：蓝牙广播状态闪烁

## ✅ **修复验证**

### 我们的修复方案正确性
通过将电源LED改为PB14，我们已经解决了：
1. ✅ **与背光系统的冲突** (PB12)
2. ✅ **与蓝牙状态LED的冲突** (PB12)
3. ✅ **避免了多个系统同时控制同一引脚**

### 各系统的LED使用情况
- **电源LED**: PB14 (修复后)
- **蓝牙状态LED**: PB12 (Scroll Lock)
- **背光系统**: PB12 (LED_SCR), PA12 (LED_CAP), PB13 (LED_NUM)
- **RF配对LED**: PA7 (HAL LED系统)

## 🎯 **蓝牙LED控制的工作流程**

### BLE模式下的LED行为
1. **广播阶段**: 
   - `led_flash=1` → PB12每秒闪烁一次
   - 持续120秒或直到连接成功

2. **连接成功**:
   - `led_flash=0` → 停止闪烁
   - `set_led_scr(1)` → PB12常亮3秒
   - 然后根据主机LED状态控制

3. **断开连接**:
   - 重新开始广播流程

### RF模式下的LED行为
- 根据接收到的数据包控制LED状态
- 同步主机的Num/Caps/Scroll Lock状态

## 📝 **建议和注意事项**

### 1. 硬件连接确认
确保电源LED已从PB12改接到PB14引脚。

### 2. 功能测试
- **BLE模式**: 观察蓝牙广播和连接时的LED行为
- **RF模式**: 测试LED状态同步功能
- **电源LED**: 确认上电和唤醒指示正常

### 3. 调试建议
可以通过串口日志观察：
- 蓝牙状态变化
- LED控制函数调用
- 电源管理事件

### 4. 潜在改进
如果需要完全避免冲突，可以考虑：
- 为蓝牙状态使用独立的LED引脚
- 或者在睡眠模式下禁用蓝牙状态LED

## 总结

蓝牙连接相关的程序确实有大量控制LED的代码，主要集中在：
1. **连接状态指示** - 广播时闪烁，连接时常亮
2. **HID LED同步** - 同步主机的键盘LED状态
3. **RF通信LED同步** - 2.4G模式下的LED状态同步

这些LED控制都使用PB12引脚，与我们原来的电源LED产生了冲突，导致了LED闪烁异常的问题。通过将电源LED改为PB14，我们成功解决了这个冲突。
